from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from datetime import datetime, timedelta
import io
import csv
import xlsxwriter

bp = Blueprint('main', __name__)

def sync_quote_items_to_related_documents(quote, new_item=None):
    """Synchronize quote items with related invoice and delivery note"""
    import app.models as models
    from app import db

    # Find related invoice
    invoice = models.Invoice.query.filter_by(quote_id=quote.id).first()
    if invoice:
        if new_item:
            # Add the new item to invoice
            invoice_item = models.InvoiceItem(
                invoice_id=invoice.id,
                product_id=new_item.product_id,
                description=new_item.description,
                quantity=new_item.quantity,
                unit_price=new_item.unit_price,
                total=new_item.total
            )
            db.session.add(invoice_item)

            # Find related delivery note
            delivery_note = models.DeliveryNote.query.filter_by(invoice_id=invoice.id).first()
            if delivery_note:
                # Add the new item to delivery note
                delivery_note_item = models.DeliveryNoteItem(
                    delivery_note_id=delivery_note.id,
                    product_id=new_item.product_id,
                    description=new_item.description,
                    quantity=new_item.quantity
                )
                db.session.add(delivery_note_item)

        # Recalculate invoice totals
        invoice.calculate_totals()
        db.session.flush()

def sync_quote_item_removal(quote, removed_item):
    """Remove corresponding items from related invoice and delivery note"""
    import app.models as models
    from app import db

    # Find related invoice
    invoice = models.Invoice.query.filter_by(quote_id=quote.id).first()
    if invoice:
        # Find and remove corresponding invoice item
        invoice_item = models.InvoiceItem.query.filter_by(
            invoice_id=invoice.id,
            product_id=removed_item.product_id
        ).first()
        if invoice_item:
            db.session.delete(invoice_item)

        # Find related delivery note
        delivery_note = models.DeliveryNote.query.filter_by(invoice_id=invoice.id).first()
        if delivery_note:
            # Find and remove corresponding delivery note item
            delivery_note_item = models.DeliveryNoteItem.query.filter_by(
                delivery_note_id=delivery_note.id,
                product_id=removed_item.product_id
            ).first()
            if delivery_note_item:
                db.session.delete(delivery_note_item)

        # Recalculate invoice totals
        invoice.calculate_totals()
        db.session.flush()

def generate_quote_number():
    """Generate unique quote number"""
    import random
    import string
    prefix = "DEV-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

def generate_invoice_number():
    """Generate unique invoice number"""
    import random
    import string
    prefix = "FAC-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

def generate_delivery_note_number():
    """Generate unique delivery note number"""
    import random
    import string
    prefix = "BL-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

def approve_delivery_note_and_update_stock(delivery_note_id):
    """Approve delivery note and automatically update stock"""
    import app.models as models
    from app import db

    delivery_note = models.DeliveryNote.query.get(delivery_note_id)
    if not delivery_note:
        return False, "Bon de livraison introuvable"

    if delivery_note.status == 'delivered':
        return False, "Bon de livraison déjà livré"

    try:
        # Update delivery note status
        delivery_note.status = 'delivered'

        # Create stock movements for each item
        for item in delivery_note.items:
            if item.product_id and item.quantity > 0:
                product = models.Product.query.get(item.product_id)
                if product:
                    # Check if enough stock available
                    if product.current_quantity < item.quantity:
                        return False, f"Stock insuffisant pour {product.name}. Disponible: {product.current_quantity}, Demandé: {item.quantity}"

                    # Create stock movement
                    movement = models.StockMovement(
                        product_id=item.product_id,
                        movement_type='out',
                        quantity=item.quantity,
                        reference_document=delivery_note.delivery_note_number,
                        delivery_note_id=delivery_note.id,
                        notes=f"Sortie automatique - Livraison {delivery_note.delivery_note_number}"
                    )
                    db.session.add(movement)

                    # Update product quantity
                    product.current_quantity -= item.quantity

        db.session.commit()
        return True, f"Bon de livraison approuvé et stock mis à jour"

    except Exception as e:
        db.session.rollback()
        return False, f"Erreur lors de la mise à jour: {str(e)}"

@bp.route('/')
@bp.route('/index')
def index():
    """Dashboard with real statistics"""
    from flask import session

    # Check if user is logged in
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))

    try:
        import app.models as models

        # Get real statistics
        products_count = models.Product.query.count()
        low_stock_products = models.Product.query.filter(models.Product.current_quantity <= 5).count()

        try:
            quotes_count = models.Quote.query.count()
        except:
            quotes_count = 0

        try:
            invoices_count = models.Invoice.query.count()
        except:
            invoices_count = 0

        try:
            delivery_notes_count = models.DeliveryNote.query.count()
        except:
            delivery_notes_count = 0

        # Get recent stock movements
        try:
            recent_movements = models.StockMovement.query.order_by(models.StockMovement.date.desc()).limit(5).all()
        except:
            recent_movements = []

    except Exception as e:
        # Fallback to dummy data if models not available
        products_count = 0
        low_stock_products = 0
        quotes_count = 0
        invoices_count = 0
        delivery_notes_count = 0
        recent_movements = []

    return render_template('index.html',
                           products_count=products_count,
                           low_stock_products=low_stock_products,
                           quotes_count=quotes_count,
                           invoices_count=invoices_count,
                           delivery_notes_count=delivery_notes_count,
                           recent_movements=recent_movements)

@bp.route('/view_template/<template_name>')
def view_template(template_name):
    from app.models.company import Company

    # Get company information
    company = Company.query.first()

    # Create dummy data for templates
    if template_name == 'quote':
        # Dummy quote data
        class DummyProduct:
            def __init__(self, name, reference):
                self.name = name
                self.reference = reference

        class DummyQuoteItem:
            def __init__(self, product, quantity, unit_price, discount):
                self.product = product
                self.quantity = quantity
                self.unit_price = unit_price
                self.discount = discount
                self.total = quantity * unit_price * (1 - discount / 100)

        class DummyClient:
            def __init__(self, name, address, email, phone):
                self.name = name
                self.address = address
                self.email = email
                self.phone = phone

        class DummyQuote:
            def __init__(self, quote_number, date, expiration_date, status, tax_rate, notes, client, items):
                self.quote_number = quote_number
                self.date = date
                self.expiration_date = expiration_date
                self.status = status
                self.tax_rate = tax_rate
                self.notes = notes
                self.client = client
                self.items = items
                self.subtotal = sum(item.total for item in items)
                self.tax_amount = self.subtotal * (tax_rate / 100)
                self.total = self.subtotal + self.tax_amount

        # Create dummy objects
        client = DummyClient(
            'Client Test',
            '123 Rue de Test, Casablanca',
            '<EMAIL>',
            '0600000000'
        )

        items = [
            DummyQuoteItem(
                DummyProduct('Extincteur à eau pulvérisée 6L', 'EXT-EAU-6L'),
                2,
                450.00,
                0
            ),
            DummyQuoteItem(
                DummyProduct('Extincteur CO2 2kg', 'EXT-CO2-2KG'),
                1,
                650.00,
                0
            )
        ]

        quote = DummyQuote(
            'DEVIS-TEST-001',
            datetime.now(),
            datetime.now() + timedelta(days=30),
            'draft',
            20.0,
            'Devis de test',
            client,
            items
        )
        return render_template('quotes/print_exact.html', quote=quote, company=company)

    elif template_name == 'invoice':
        # Dummy invoice data
        class DummyProduct:
            def __init__(self, name, reference):
                self.name = name
                self.reference = reference

        class DummyInvoiceItem:
            def __init__(self, product, quantity, unit_price, discount):
                self.product = product
                self.quantity = quantity
                self.unit_price = unit_price
                self.discount = discount
                self.total = quantity * unit_price * (1 - discount / 100)

        class DummyClient:
            def __init__(self, name, address, email, phone):
                self.name = name
                self.address = address
                self.email = email
                self.phone = phone

        class DummyInvoice:
            def __init__(self, invoice_number, date, due_date, status, tax_rate, notes, client, items):
                self.invoice_number = invoice_number
                self.date = date
                self.due_date = due_date
                self.status = status
                self.tax_rate = tax_rate
                self.notes = notes
                self.client = client
                self.items = items
                self.subtotal = sum(item.total for item in items)
                self.tax_amount = self.subtotal * (tax_rate / 100)
                self.total = self.subtotal + self.tax_amount

        # Create dummy objects
        client = DummyClient(
            'Client Test',
            '123 Rue de Test, Casablanca',
            '<EMAIL>',
            '0600000000'
        )

        items = [
            DummyInvoiceItem(
                DummyProduct('Extincteur à eau pulvérisée 6L', 'EXT-EAU-6L'),
                2,
                450.00,
                0
            ),
            DummyInvoiceItem(
                DummyProduct('Extincteur CO2 2kg', 'EXT-CO2-2KG'),
                1,
                650.00,
                0
            )
        ]

        invoice = DummyInvoice(
            'FACTURE-TEST-001',
            datetime.now(),
            datetime.now() + timedelta(days=30),
            'draft',
            20.0,
            'Facture de test',
            client,
            items
        )
        return render_template('invoices/print_exact.html', invoice=invoice, company=company)

    elif template_name == 'delivery_note':
        # Dummy delivery note data
        class DummyProduct:
            def __init__(self, name, reference):
                self.name = name
                self.reference = reference

        class DummyDeliveryNoteItem:
            def __init__(self, product, quantity):
                self.product = product
                self.quantity = quantity

        class DummyClient:
            def __init__(self, name, address, email, phone):
                self.name = name
                self.address = address
                self.email = email
                self.phone = phone

        class DummyDeliveryNote:
            def __init__(self, delivery_note_number, date, status, notes, client, items):
                self.delivery_note_number = delivery_note_number
                self.date = date
                self.status = status
                self.notes = notes
                self.client = client
                self.items = items
                self.invoice = None

        # Create dummy objects
        client = DummyClient(
            'Client Test',
            '123 Rue de Test, Casablanca',
            '<EMAIL>',
            '0600000000'
        )

        items = [
            DummyDeliveryNoteItem(
                DummyProduct('Extincteur à eau pulvérisée 6L', 'EXT-EAU-6L'),
                2
            ),
            DummyDeliveryNoteItem(
                DummyProduct('Extincteur CO2 2kg', 'EXT-CO2-2KG'),
                1
            )
        ]

        delivery_note = DummyDeliveryNote(
            'BL-TEST-001',
            datetime.now(),
            'draft',
            'Bon de livraison de test',
            client,
            items
        )
        return render_template('pdf/delivery_note.html', delivery_note=delivery_note, company=company)

    # If template name is not recognized
    return 'Template not found', 404

# Routes pour la gestion commerciale
@bp.route('/commercial/quotes')
def commercial_quotes():
    from app.models import Quote
    quotes = Quote.query.order_by(Quote.date.desc()).all()
    return render_template('quotes/index.html', quotes=quotes)

# Redirect to quotes blueprint for creation
@bp.route('/commercial/quotes/create')
def commercial_quotes_create():
    return redirect(url_for('quotes.create'))

@bp.route('/commercial/quotes/<int:quote_id>/create_invoice', methods=['GET', 'POST'])
def commercial_quotes_create_invoice(quote_id):
    """Create invoice from quote"""
    import app.models as models
    from app.forms.quote_forms import InvoiceForm
    import random
    import string

    quote = models.Quote.query.get_or_404(quote_id)

    def generate_invoice_number():
        prefix = "FAC-"
        date_part = datetime.now().strftime("%Y%m")
        random_part = ''.join(random.choices(string.digits, k=4))
        return f"{prefix}{date_part}-{random_part}"

    form = InvoiceForm()
    form.client_id.choices = [(c.id, c.name) for c in models.Client.query.all()]
    form.quote_id.choices = [(0, 'Aucun')] + [(q.id, f"{q.quote_number} - {q.client.name}") for q in models.Quote.query.filter_by(status='accepted').all()]

    # Pre-fill form with quote data
    if request.method == 'GET':
        form.client_id.data = quote.client_id
        form.quote_id.data = quote.id
        form.tax_rate.data = quote.tax_rate
        form.notes.data = quote.notes

    if form.validate_on_submit():
        invoice = models.Invoice(
            invoice_number=generate_invoice_number(),
            client_id=form.client_id.data,
            quote_id=quote.id,
            date=form.date.data or datetime.now(),
            due_date=form.due_date.data or (datetime.now() + timedelta(days=30)),
            status=form.status.data,
            tax_rate=form.tax_rate.data,
            notes=form.notes.data
        )

        from app import db
        db.session.add(invoice)
        db.session.flush()  # Get the ID

        # Copy items from quote
        for quote_item in quote.items:
            invoice_item = models.InvoiceItem(
                invoice_id=invoice.id,
                product_id=quote_item.product_id,
                quantity=quote_item.quantity,
                unit_price=quote_item.unit_price,
                total=quote_item.total
            )
            db.session.add(invoice_item)

        # Update quote status
        quote.status = 'invoiced'

        db.session.commit()

        # Calculate totals
        invoice.calculate_totals()
        db.session.commit()

        flash(f'Facture créée avec succès à partir du devis {quote.quote_number}!', 'success')
        return redirect(url_for('main.commercial_invoices_edit', id=invoice.id))

    return render_template('invoices/create.html', form=form, quote=quote)

@bp.route('/commercial/quotes/<int:id>/edit', methods=['GET', 'POST'])
def commercial_quotes_edit(id):
    import app.models as models
    from app.forms.quote_forms import QuoteItemForm
    from app import db

    quote = models.Quote.query.get_or_404(id)
    form = QuoteItemForm()
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference}) - {p.unit_price} MAD") for p in models.Product.query.all()]

    if form.validate_on_submit():
        # التحقق من أن القيم ليست None
        quantity = form.quantity.data or 0
        unit_price = form.unit_price.data or 0.0

        # إذا لم يتم تحديد السعر، استخدم سعر المنتج الافتراضي
        if unit_price == 0.0:
            product = models.Product.query.get(form.product_id.data)
            if product:
                unit_price = product.unit_price

        total = quantity * unit_price

        item = models.QuoteItem(
            quote_id=quote.id,
            product_id=form.product_id.data,
            quantity=quantity,
            unit_price=unit_price,
            total=total
        )

        db.session.add(item)

        # Auto-sync with related invoice and delivery note
        sync_quote_items_to_related_documents(quote, item)

        quote.calculate_totals()
        db.session.commit()

        flash('Produit ajouté au devis et synchronisé avec la facture et le bon de livraison!', 'success')
        return redirect(url_for('main.commercial_quotes_edit', id=quote.id))

    return render_template('quotes/edit.html', quote=quote, form=form)

@bp.route('/commercial/quotes/<int:quote_id>/remove_item/<int:item_id>', methods=['POST'])
def commercial_quotes_remove_item(quote_id, item_id):
    import app.models as models
    from app import db

    quote = models.Quote.query.get_or_404(quote_id)
    item = models.QuoteItem.query.get_or_404(item_id)

    if item.quote_id == quote.id:
        # Remove from related documents first
        sync_quote_item_removal(quote, item)

        db.session.delete(item)
        quote.calculate_totals()
        db.session.commit()
        flash('Produit retiré du devis et des documents liés!', 'success')

    return redirect(url_for('main.commercial_quotes_edit', id=quote_id))

@bp.route('/commercial/invoices')
def commercial_invoices():
    import app.models as models
    invoices = models.Invoice.query.order_by(models.Invoice.date.desc()).all()
    return render_template('invoices/index.html', invoices=invoices)

@bp.route('/commercial/invoices/create', methods=['GET', 'POST'])
def commercial_invoices_create():
    import app.models as models
    from app.forms.quote_forms import InvoiceForm
    import random
    import string

    def generate_invoice_number():
        prefix = "FAC-"
        date_part = datetime.now().strftime("%Y%m")
        random_part = ''.join(random.choices(string.digits, k=4))
        return f"{prefix}{date_part}-{random_part}"

    form = InvoiceForm()
    form.client_id.choices = [(c.id, c.name) for c in models.Client.query.all()]
    form.quote_id.choices = [(0, 'Aucun')] + [(q.id, f"{q.quote_number} - {q.client.name}") for q in models.Quote.query.filter_by(status='accepted').all()]

    if form.validate_on_submit():
        invoice = models.Invoice(
            invoice_number=generate_invoice_number(),
            client_id=form.client_id.data,
            quote_id=form.quote_id.data if form.quote_id.data != 0 else None,
            date=form.date.data or datetime.now(),
            due_date=form.due_date.data or (datetime.now() + timedelta(days=30)),
            status=form.status.data,
            tax_rate=form.tax_rate.data,
            notes=form.notes.data
        )

        from app import db
        db.session.add(invoice)
        db.session.commit()

        flash('Facture créée avec succès! Ajoutez maintenant des produits.', 'success')
        return redirect(url_for('main.commercial_invoices_edit', id=invoice.id))

    return render_template('invoices/create.html', form=form)

@bp.route('/commercial/invoices/<int:invoice_id>/create_delivery_note', methods=['GET', 'POST'])
def commercial_invoices_create_delivery_note(invoice_id):
    """Create delivery note from invoice"""
    import app.models as models
    from app.forms.delivery_note_forms import DeliveryNoteForm
    from app import db
    import string
    import random

    invoice = models.Invoice.query.get_or_404(invoice_id)

    form = DeliveryNoteForm()

    # Populate choices
    form.client_id.choices = [(0, 'Sélectionner un client')] + [(c.id, c.name) for c in models.Client.query.all()]
    form.invoice_id.choices = [(0, 'Aucune facture')] + [(i.id, f"{i.invoice_number} - {i.client.name}") for i in models.Invoice.query.all()]

    # Pre-fill form with invoice data
    if request.method == 'GET':
        form.client_id.data = invoice.client_id
        form.invoice_id.data = invoice.id

    if form.validate_on_submit():
        # Generate delivery note number
        prefix = "BL-"
        date_part = datetime.now().strftime("%Y%m")
        random_part = ''.join(random.choices(string.digits, k=4))
        delivery_note_number = f"{prefix}{date_part}-{random_part}"

        delivery_note = models.DeliveryNote(
            delivery_note_number=delivery_note_number,
            client_id=form.client_id.data,
            invoice_id=invoice.id,
            date=form.date.data or datetime.now().date(),
            status=form.status.data,
            notes=form.notes.data
        )

        db.session.add(delivery_note)
        db.session.flush()  # To get the ID

        # Copy items from invoice
        for invoice_item in invoice.items:
            delivery_note_item = models.DeliveryNoteItem(
                delivery_note_id=delivery_note.id,
                product_id=invoice_item.product_id,
                quantity=invoice_item.quantity
            )
            db.session.add(delivery_note_item)

        db.session.commit()
        flash(f'Bon de livraison créé avec succès à partir de la facture {invoice.invoice_number}!', 'success')
        return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note.id))

    return render_template('commercial/delivery_notes_create.html', form=form, invoice=invoice)

# Equipment Maintenance Routes
@bp.route('/equipment/maintenance')
def equipment_maintenance():
    """List all equipment maintenance records"""
    from app.models import EquipmentMaintenance
    from app.forms.equipment_forms import EquipmentSearchForm

    form = EquipmentSearchForm()

    # Base query
    query = EquipmentMaintenance.query

    # Apply filters if form is submitted
    if form.validate_on_submit():
        if form.search.data:
            query = query.filter(EquipmentMaintenance.designation.contains(form.search.data))

        if form.situation_filter.data:
            query = query.filter(EquipmentMaintenance.current_situation == form.situation_filter.data)

        if form.alert_filter.data:
            if form.alert_filter.data == 'expired':
                # Filter expired items
                expired_items = []
                for item in query.all():
                    if item.is_expired():
                        expired_items.append(item.id)
                query = query.filter(EquipmentMaintenance.id.in_(expired_items))
            elif form.alert_filter.data == 'alert':
                # Filter items needing alert
                alert_items = []
                for item in query.all():
                    if item.needs_alert():
                        alert_items.append(item.id)
                query = query.filter(EquipmentMaintenance.id.in_(alert_items))
            elif form.alert_filter.data == 'ok':
                # Filter OK items
                ok_items = []
                for item in query.all():
                    if not item.is_expired() and not item.needs_alert():
                        ok_items.append(item.id)
                query = query.filter(EquipmentMaintenance.id.in_(ok_items))

    equipment_list = query.order_by(EquipmentMaintenance.created_at.desc()).all()

    # Calculate statistics
    total_count = len(equipment_list)
    expired_count = sum(1 for item in equipment_list if item.is_expired())
    alert_count = sum(1 for item in equipment_list if item.needs_alert())
    ok_count = total_count - expired_count - alert_count

    # Get all alerts for the dashboard
    alerts = []
    for item in equipment_list:
        alert_msg = item.get_alert_message()
        if alert_msg:
            alerts.append({
                'message': alert_msg,
                'item': item,
                'is_expired': item.is_expired()
            })

    return render_template('equipment/maintenance.html',
                         equipment_list=equipment_list,
                         form=form,
                         total_count=total_count,
                         expired_count=expired_count,
                         alert_count=alert_count,
                         ok_count=ok_count,
                         alerts=alerts)

@bp.route('/equipment/maintenance/create', methods=['GET', 'POST'])
def equipment_maintenance_create():
    """Create new equipment maintenance record"""
    from app.models import EquipmentMaintenance
    from app.forms.equipment_forms import EquipmentMaintenanceForm
    from app import db

    form = EquipmentMaintenanceForm()

    if form.validate_on_submit():
        # Create equipment records for each item
        equipment_count = 0
        for item_form in form.equipment_items:
            if item_form.designation.data and item_form.quantity.data:
                equipment = EquipmentMaintenance(
                    client_id=form.client_id.data,
                    designation=item_form.designation.data,
                    quantity=item_form.quantity.data,
                    supply_date=form.supply_date.data,
                    verification_date=form.verification_date.data,
                    current_situation=form.current_situation.data,
                    warranty_end_date=form.warranty_end_date.data,
                    recharge_end_date=form.recharge_end_date.data,
                    verification_end_date=form.verification_end_date.data,
                    replacement_end_date=form.replacement_end_date.data,
                    notes=form.notes.data
                )

                db.session.add(equipment)
                equipment_count += 1

        db.session.commit()

        flash(f'{equipment_count} équipement(s) ajouté(s) avec succès!', 'success')
        return redirect(url_for('main.equipment_maintenance'))

    return render_template('equipment/maintenance_create.html', form=form)

@bp.route('/equipment/maintenance/<int:id>/edit', methods=['GET', 'POST'])
def equipment_maintenance_edit(id):
    """Edit equipment maintenance record"""
    from app.models import EquipmentMaintenance
    from app.forms.equipment_forms import EquipmentEditForm
    from app import db

    equipment = EquipmentMaintenance.query.get_or_404(id)
    form = EquipmentEditForm(obj=equipment)

    if form.validate_on_submit():
        form.populate_obj(equipment)
        equipment.updated_at = datetime.now()

        db.session.commit()

        flash(f'Équipement "{equipment.designation}" modifié avec succès!', 'success')
        return redirect(url_for('main.equipment_maintenance'))

    return render_template('equipment/maintenance_edit.html', form=form, equipment=equipment)

@bp.route('/equipment/maintenance/<int:id>/delete', methods=['POST'])
def equipment_maintenance_delete(id):
    """Delete equipment maintenance record"""
    from app.models import EquipmentMaintenance
    from app import db

    equipment = EquipmentMaintenance.query.get_or_404(id)
    designation = equipment.designation

    db.session.delete(equipment)
    db.session.commit()

    flash(f'Équipement "{designation}" supprimé avec succès!', 'success')
    return redirect(url_for('main.equipment_maintenance'))

@bp.route('/commercial/invoices/<int:id>/edit', methods=['GET', 'POST'])
def commercial_invoices_edit(id):
    import app.models as models
    from app.forms.quote_forms import InvoiceItemForm
    from app import db

    invoice = models.Invoice.query.get_or_404(id)
    form = InvoiceItemForm()
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference}) - {p.unit_price} MAD") for p in models.Product.query.all()]

    if form.validate_on_submit():
        # التحقق من أن القيم ليست None
        quantity = form.quantity.data or 0
        unit_price = form.unit_price.data or 0.0

        # إذا لم يتم تحديد السعر، استخدم سعر المنتج الافتراضي
        if unit_price == 0.0:
            product = models.Product.query.get(form.product_id.data)
            if product:
                unit_price = product.unit_price

        total = quantity * unit_price

        item = models.InvoiceItem(
            invoice_id=invoice.id,
            product_id=form.product_id.data,
            quantity=quantity,
            unit_price=unit_price,
            total=total
        )

        db.session.add(item)
        invoice.calculate_totals()
        db.session.commit()

        flash('Produit ajouté à la facture!', 'success')
        return redirect(url_for('main.commercial_invoices_edit', id=invoice.id))

    return render_template('invoices/edit.html', invoice=invoice, form=form)

@bp.route('/commercial/invoices/<int:invoice_id>/remove_item/<int:item_id>', methods=['POST'])
def commercial_invoices_remove_item(invoice_id, item_id):
    import app.models as models
    from app import db

    invoice = models.Invoice.query.get_or_404(invoice_id)
    item = models.InvoiceItem.query.get_or_404(item_id)

    if item.invoice_id == invoice.id:
        db.session.delete(item)
        invoice.calculate_totals()
        db.session.commit()
        flash('Produit retiré de la facture!', 'success')

    return redirect(url_for('main.commercial_invoices_edit', id=invoice_id))

@bp.route('/commercial/delivery_notes')
def commercial_delivery_notes():
    import app.models as models
    delivery_notes = models.DeliveryNote.query.order_by(models.DeliveryNote.date.desc()).all()
    return render_template('delivery_notes/index.html', delivery_notes=delivery_notes)

@bp.route('/commercial/delivery_notes/create', methods=['GET', 'POST'])
def commercial_delivery_notes_create():
    import app.models as models
    from app.forms.delivery_note_forms import DeliveryNoteForm
    from app import db
    from datetime import datetime
    import string
    import random

    form = DeliveryNoteForm()

    # Populate choices
    form.client_id.choices = [(0, 'Sélectionner un client')] + [(c.id, c.name) for c in models.Client.query.all()]
    form.invoice_id.choices = [(0, 'Aucune facture')] + [(i.id, f"{i.invoice_number} - {i.client.name}") for i in models.Invoice.query.all()]

    if form.validate_on_submit():
        # Generate delivery note number
        prefix = "BL-"
        date_part = datetime.now().strftime("%Y%m")
        random_part = ''.join(random.choices(string.digits, k=4))
        delivery_note_number = f"{prefix}{date_part}-{random_part}"

        delivery_note = models.DeliveryNote(
            delivery_note_number=delivery_note_number,
            client_id=form.client_id.data,
            date=form.date.data or datetime.now().date(),
            status=form.status.data,
            notes=form.notes.data
        )

        db.session.add(delivery_note)
        db.session.flush()  # To get the ID

        # If based on an invoice, copy items
        if form.invoice_id.data and form.invoice_id.data != 0:
            invoice = models.Invoice.query.get(form.invoice_id.data)
            if invoice:
                for invoice_item in invoice.items:
                    delivery_note_item = models.DeliveryNoteItem(
                        delivery_note_id=delivery_note.id,
                        product_id=invoice_item.product_id,
                        quantity=invoice_item.quantity
                    )
                    db.session.add(delivery_note_item)

        db.session.commit()
        flash('Bon de livraison créé avec succès!', 'success')
        return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note.id))

    # Get statistics for the sidebar
    delivery_notes_count = models.DeliveryNote.query.count()
    delivered_count = models.DeliveryNote.query.filter_by(status='delivered').count()

    return render_template('delivery_notes/create.html',
                         form=form,
                         delivery_notes_count=delivery_notes_count,
                         delivered_count=delivered_count)

@bp.route('/commercial/delivery_notes/<int:id>/edit', methods=['GET', 'POST'])
def commercial_delivery_notes_edit(id):
    import app.models as models
    from app.forms.delivery_note_forms import DeliveryNoteForm, DeliveryNoteItemForm
    from app import db

    delivery_note = models.DeliveryNote.query.get_or_404(id)
    form = DeliveryNoteForm(obj=delivery_note)
    item_form = DeliveryNoteItemForm()

    # Populate choices
    form.client_id.choices = [(c.id, c.name) for c in models.Client.query.all()]
    item_form.product_id.choices = [(0, 'Sélectionner un produit')] + [(p.id, f"{p.name} ({p.reference})") for p in models.Product.query.all()]

    if form.validate_on_submit():
        # Check for quick actions
        if request.form.get('quick_action') == 'mark_delivered':
            delivery_note.status = 'delivered'
            db.session.commit()
            flash('Bon de livraison marqué comme livré!', 'success')
            return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note.id))

        # Regular form submission
        delivery_note.client_id = form.client_id.data
        delivery_note.date = form.date.data
        delivery_note.status = form.status.data
        delivery_note.notes = form.notes.data

        db.session.commit()
        flash('Bon de livraison modifié avec succès!', 'success')
        return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note.id))

    return render_template('delivery_notes/edit.html',
                         delivery_note=delivery_note,
                         form=form,
                         item_form=item_form)

@bp.route('/commercial/delivery_notes/<int:id>/add_item', methods=['POST'])
def commercial_delivery_notes_add_item(id):
    import app.models as models
    from app.forms.delivery_note_forms import DeliveryNoteItemForm
    from app import db

    delivery_note = models.DeliveryNote.query.get_or_404(id)
    form = DeliveryNoteItemForm()

    if form.validate_on_submit():
        # Check if product already exists in delivery note
        existing_item = models.DeliveryNoteItem.query.filter_by(
            delivery_note_id=delivery_note.id,
            product_id=form.product_id.data
        ).first()

        if existing_item:
            existing_item.quantity += form.quantity.data
        else:
            item = models.DeliveryNoteItem(
                delivery_note_id=delivery_note.id,
                product_id=form.product_id.data,
                quantity=form.quantity.data
            )
            db.session.add(item)

        db.session.commit()
        flash('Produit ajouté au bon de livraison!', 'success')

    return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note.id))

@bp.route('/commercial/delivery_notes/<int:delivery_note_id>/remove_item/<int:item_id>', methods=['POST'])
def commercial_delivery_notes_remove_item(delivery_note_id, item_id):
    import app.models as models
    from app import db

    delivery_note = models.DeliveryNote.query.get_or_404(delivery_note_id)
    item = models.DeliveryNoteItem.query.get_or_404(item_id)

    if item.delivery_note_id == delivery_note.id:
        db.session.delete(item)
        db.session.commit()
        flash('Produit retiré du bon de livraison!', 'success')

    return redirect(url_for('main.commercial_delivery_notes_edit', id=delivery_note_id))

@bp.route('/commercial/delivery_notes/<int:id>/approve', methods=['POST'])
def commercial_delivery_notes_approve(id):
    """Approve delivery note and update stock automatically"""
    success, message = approve_delivery_note_and_update_stock(id)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('main.commercial_delivery_notes_edit', id=id))

@bp.route('/commercial/delivery_notes/<int:id>/delete', methods=['POST'])
def commercial_delivery_notes_delete(id):
    import app.models as models
    from app import db

    delivery_note = models.DeliveryNote.query.get_or_404(id)

    # Delete associated items first
    for item in delivery_note.items:
        db.session.delete(item)

    # Delete the delivery note
    db.session.delete(delivery_note)
    db.session.commit()

    flash('Bon de livraison supprimé avec succès!', 'success')
    return redirect(url_for('main.commercial_delivery_notes'))

# Direct routes for products
@bp.route('/products')
def products():
    """List all products"""
    import app.models as models
    products = models.Product.query.all()
    return render_template('products/index.html', products=products)

@bp.route('/products/create', methods=['GET', 'POST'])
def products_create():
    """Create a new product"""
    import app.models as models
    from app.forms.product_forms import ProductForm
    from app import db

    form = ProductForm()

    if form.validate_on_submit():
        # Check if reference already exists
        existing_product = models.Product.query.filter_by(reference=form.reference.data).first()
        if existing_product:
            flash(f'Erreur: La référence "{form.reference.data}" existe déjà. Veuillez utiliser une référence unique.', 'error')
            return render_template('products/create.html', form=form)

        try:
            product = models.Product(
                name=form.name.data,
                product_type=form.product_type.data,
                reference=form.reference.data,
                unit_price=form.unit_price.data,
                current_quantity=form.current_quantity.data or 0,
                description=form.description.data
            )

            db.session.add(product)
            db.session.commit()

            flash('Produit créé avec succès!', 'success')
            return redirect(url_for('main.products'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création du produit: {str(e)}', 'error')
            return render_template('products/create.html', form=form)

    return render_template('products/create.html', form=form)

@bp.route('/products/<int:id>/edit', methods=['GET', 'POST'])
def products_edit(id):
    """Edit a product"""
    import app.models as models
    from app.forms.product_forms import ProductForm
    from app import db

    product = models.Product.query.get_or_404(id)
    form = ProductForm(obj=product)

    if form.validate_on_submit():
        # Check if reference already exists (excluding current product)
        existing_product = models.Product.query.filter(
            models.Product.reference == form.reference.data,
            models.Product.id != id
        ).first()

        if existing_product:
            flash(f'Erreur: La référence "{form.reference.data}" existe déjà. Veuillez utiliser une référence unique.', 'error')
            return render_template('products/edit.html', form=form, product=product)

        try:
            product.name = form.name.data
            product.product_type = form.product_type.data
            product.reference = form.reference.data
            product.unit_price = form.unit_price.data
            product.current_quantity = form.current_quantity.data or 0
            product.description = form.description.data

            db.session.commit()
            flash('Produit modifié avec succès!', 'success')
            return redirect(url_for('main.products'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la modification du produit: {str(e)}', 'error')
            return render_template('products/edit.html', form=form, product=product)

    return render_template('products/edit.html', form=form, product=product)

@bp.route('/products/<int:id>/delete', methods=['POST'])
def products_delete(id):
    """Delete a product"""
    import app.models as models
    from app import db

    product = models.Product.query.get_or_404(id)
    db.session.delete(product)
    db.session.commit()

    flash('Produit supprimé avec succès!', 'success')
    return redirect(url_for('main.products'))

# Redirect stock to products (merged)
@bp.route('/stock')
def stock():
    """Redirect to products page (stock is now integrated)"""
    return redirect(url_for('main.products'))

@bp.route('/stock/add', methods=['POST'])
def stock_add():
    """Add stock via AJAX from products page"""
    import app.models as models
    from app import db

    try:
        product_id = request.form.get('product_id')
        quantity = int(request.form.get('quantity', 0))
        reference_document = request.form.get('reference_document', '')
        notes = request.form.get('notes', '')

        if quantity <= 0:
            flash('La quantité doit être positive!', 'error')
            return redirect(url_for('main.products'))

        # Get the product
        product = models.Product.query.get_or_404(product_id)

        # Create stock movement
        try:
            movement = models.StockMovement(
                product_id=product.id,
                movement_type='in',
                quantity=quantity,
                reference_document=reference_document,
                notes=notes
            )
            db.session.add(movement)
        except:
            pass  # Skip if StockMovement model doesn't exist

        # Update product quantity
        product.current_quantity += quantity
        db.session.commit()

        flash(f'Stock ajouté avec succès! Nouvelle quantité: {product.current_quantity}', 'success')
        return redirect(url_for('main.products'))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout du stock: {str(e)}', 'error')
        return redirect(url_for('main.products'))

@bp.route('/stock/remove', methods=['POST'])
def stock_remove():
    """Remove stock via AJAX from products page"""
    import app.models as models
    from app import db

    try:
        product_id = request.form.get('product_id')
        quantity = int(request.form.get('quantity', 0))
        reference_document = request.form.get('reference_document', '')
        notes = request.form.get('notes', '')

        if quantity <= 0:
            flash('La quantité doit être positive!', 'error')
            return redirect(url_for('main.products'))

        # Get the product
        product = models.Product.query.get_or_404(product_id)

        # Check if enough stock available
        if product.current_quantity < quantity:
            flash(f'Stock insuffisant! Stock disponible: {product.current_quantity}', 'error')
            return redirect(url_for('main.products'))

        # Create stock movement
        try:
            movement = models.StockMovement(
                product_id=product.id,
                movement_type='out',
                quantity=quantity,
                reference_document=reference_document,
                notes=notes
            )
            db.session.add(movement)
        except:
            pass  # Skip if StockMovement model doesn't exist

        # Update product quantity
        product.current_quantity -= quantity
        db.session.commit()

        flash(f'Stock retiré avec succès! Nouvelle quantité: {product.current_quantity}', 'success')
        return redirect(url_for('main.products'))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors du retrait du stock: {str(e)}', 'error')
        return redirect(url_for('main.products'))

@bp.route('/stock/movements')
def stock_movements():
    """List all stock movements"""
    import app.models as models
    try:
        movements = models.StockMovement.query.order_by(models.StockMovement.date.desc()).all()
    except:
        movements = []
    return render_template('stock/movements.html', movements=movements)

# Direct routes for client management
@bp.route('/clients')
def clients():
    """List all clients"""
    import app.models as models
    clients = models.Client.query.all()
    return render_template('clients/index.html', clients=clients)

@bp.route('/clients/create', methods=['GET', 'POST'])
def clients_create():
    """Create a new client"""
    import app.models as models
    from app.forms.client_forms import ClientForm
    from app import db

    form = ClientForm()

    if form.validate_on_submit():
        client = models.Client(
            name=form.name.data,
            address=form.address.data,
            city=form.city.data,
            postal_code=form.postal_code.data,
            email=form.email.data,
            phone=form.phone.data,
            ice=form.ice.data
        )

        db.session.add(client)
        db.session.commit()

        flash('Client créé avec succès!', 'success')
        return redirect(url_for('main.clients'))

    return render_template('clients/create.html', form=form)

@bp.route('/clients/<int:id>/edit', methods=['GET', 'POST'])
def clients_edit(id):
    """Edit a client"""
    import app.models as models
    from app.forms.client_forms import ClientForm
    from app import db

    client = models.Client.query.get_or_404(id)
    form = ClientForm(obj=client)

    if form.validate_on_submit():
        client.name = form.name.data
        client.address = form.address.data
        client.city = form.city.data
        client.postal_code = form.postal_code.data
        client.email = form.email.data
        client.phone = form.phone.data
        client.ice = form.ice.data

        db.session.commit()
        flash('Client modifié avec succès!', 'success')
        return redirect(url_for('main.clients'))

    return render_template('clients/edit.html', form=form, client=client)

# Print Routes - Removed enhanced print routes

# Simple Print Routes
@bp.route('/quotes/<int:id>/print')
def print_quote(id):
    """Simple print view for quotes"""
    import app.models as models
    from app.models.company import Company

    try:
        quote = models.Quote.query.get_or_404(id)
    except:
        quote = None

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('quotes/print_exact.html', quote=quote, company=company)

@bp.route('/invoices/<int:id>/print')
def print_invoice(id):
    """Simple print view for invoices"""
    import app.models as models
    from app.models.company import Company

    try:
        invoice = models.Invoice.query.get_or_404(id)
    except:
        invoice = None

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('invoices/print_final.html', invoice=invoice, company=company)

@bp.route('/delivery-notes/<int:id>/print')
def print_delivery_note(id):
    """Simple print view for delivery notes"""
    import app.models as models
    from app.models.company import Company

    try:
        delivery_note = models.DeliveryNote.query.get_or_404(id)
    except:
        delivery_note = None

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('delivery_notes/print_final.html', delivery_note=delivery_note, company=company)

# Demo routes for testing print templates
@bp.route('/demo/print/quote')
def demo_print_quote():
    """Demo print view for quotes"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('quotes/print_exact.html', quote=None, company=company)

@bp.route('/demo/print/invoice')
def demo_print_invoice():
    """Demo print view for invoices"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('invoices/print_exact.html', invoice=None, company=company)

@bp.route('/demo/print/delivery-note')
def demo_print_delivery_note():
    """Demo print view for delivery notes"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('delivery_notes/print_new.html', delivery_note=None, company=company)

# Create default admin user
@bp.route('/create-admin')
def create_admin():
    """Create default admin user for testing"""
    from app.models.user import User
    from app import db
    from werkzeug.security import generate_password_hash

    try:
        # Delete existing admin if exists
        existing_admin = User.query.filter_by(username='admin').first()
        if existing_admin:
            db.session.delete(existing_admin)
            db.session.commit()

        # Create fresh admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            full_name='Administrateur',
            is_admin=True
        )
        admin.is_active = True

        db.session.add(admin)
        db.session.commit()

        return jsonify({
            'message': 'Admin user created successfully!',
            'username': 'admin',
            'password': 'admin123',
            'login_url': url_for('auth.login')
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/clients/<int:id>/delete', methods=['POST'])
def clients_delete(id):
    """Delete a client"""
    import app.models as models
    from app import db

    client = models.Client.query.get_or_404(id)

    # Check if client has associated documents or equipment
    try:
        has_quotes = hasattr(client, 'quotes') and client.quotes.count() > 0
        has_invoices = hasattr(client, 'invoices') and client.invoices.count() > 0
        has_delivery_notes = hasattr(client, 'delivery_notes') and client.delivery_notes.count() > 0
        has_equipment = hasattr(client, 'equipment_maintenance') and client.equipment_maintenance.count() > 0

        if has_quotes or has_invoices or has_delivery_notes or has_equipment:
            error_msg = 'Impossible de supprimer ce client car il a des éléments associés:'
            if has_quotes:
                error_msg += f' {client.quotes.count()} devis,'
            if has_invoices:
                error_msg += f' {client.invoices.count()} factures,'
            if has_delivery_notes:
                error_msg += f' {client.delivery_notes.count()} bons de livraison,'
            if has_equipment:
                error_msg += f' {client.equipment_maintenance.count()} équipements de maintenance,'

            # Remove trailing comma
            error_msg = error_msg.rstrip(',') + '.'

            flash(error_msg, 'error')
            return redirect(url_for('main.clients'))

    except Exception as e:
        print(f"Error checking client relationships: {e}")
        # Continue with deletion if relationship check fails
        pass

    try:
        db.session.delete(client)
        db.session.commit()
        flash('Client supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression du client: {str(e)}', 'error')

    return redirect(url_for('main.clients'))

# Routes pour l'exportation et l'importation
@bp.route('/export/<string:model_type>')
def export_data(model_type):
    if model_type == 'products':
        # Données fictives pour les produits
        data = [
            {'id': 1, 'reference': 'EXT-EAU-6L', 'name': 'Extincteur à eau pulvérisée 6L', 'current_quantity': 10, 'unit_price': 450.00},
            {'id': 2, 'reference': 'EXT-CO2-2KG', 'name': 'Extincteur CO2 2kg', 'current_quantity': 5, 'unit_price': 650.00},
            {'id': 3, 'reference': 'EXT-ABC-9KG', 'name': 'Extincteur à poudre ABC 9kg', 'current_quantity': 8, 'unit_price': 550.00}
        ]
        headers = ['ID', 'Référence', 'Nom', 'Quantité', 'Prix unitaire']
        filename = 'produits.xlsx'
    elif model_type == 'clients':
        # Données fictives pour les clients
        data = [
            {'id': 1, 'name': 'Client Test 1', 'address': '123 Rue de Test, Casablanca', 'email': '<EMAIL>', 'phone': '0600000001'},
            {'id': 2, 'name': 'Client Test 2', 'address': '456 Rue de Test, Rabat', 'email': '<EMAIL>', 'phone': '0600000002'},
            {'id': 3, 'name': 'Client Test 3', 'address': '789 Rue de Test, Marrakech', 'email': '<EMAIL>', 'phone': '0600000003'}
        ]
        headers = ['ID', 'Nom', 'Adresse', 'Email', 'Téléphone']
        filename = 'clients.xlsx'
    elif model_type == 'stock':
        # Données fictives pour le stock
        data = [
            {'id': 1, 'product_name': 'Extincteur à eau pulvérisée 6L', 'reference': 'EXT-EAU-6L', 'current_quantity': 10},
            {'id': 2, 'product_name': 'Extincteur CO2 2kg', 'reference': 'EXT-CO2-2KG', 'current_quantity': 5},
            {'id': 3, 'product_name': 'Extincteur à poudre ABC 9kg', 'reference': 'EXT-ABC-9KG', 'current_quantity': 8}
        ]
        headers = ['ID', 'Produit', 'Référence', 'Quantité']
        filename = 'stock.xlsx'
    else:
        flash('Type de données non pris en charge pour l\'exportation.', 'error')
        return redirect(url_for('main.index'))

    # Créer un fichier Excel en mémoire
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet()

    # Ajouter les en-têtes
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)

    # Ajouter les données
    for row, item in enumerate(data):
        values = list(item.values())
        for col, value in enumerate(values):
            worksheet.write(row + 1, col, value)

    workbook.close()
    output.seek(0)

    return send_file(output,
                     download_name=filename,
                     as_attachment=True,
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@bp.route('/print/<string:model_type>/<int:id>')
def print_document(model_type, id):
    import app.models as models
    from app.models.company import Company

    # Get company information
    company = Company.query.first()

    if model_type == 'quote':
        quote = models.Quote.query.get_or_404(id)
        return render_template('quotes/print_exact.html', quote=quote, company=company)

    elif model_type == 'invoice':
        invoice = models.Invoice.query.get_or_404(id)
        return render_template('invoices/print_exact.html', invoice=invoice, company=company)

    elif model_type == 'delivery_note':
        delivery_note = models.DeliveryNote.query.get_or_404(id)
        return render_template('delivery_notes/print_new.html', delivery_note=delivery_note, company=company)
    elif model_type == 'product':
        # Pour les produits, on peut afficher une fiche produit
        product = None
        for p in [
            {'id': 1, 'name': 'Extincteur à eau pulvérisée 6L', 'reference': 'EXT-EAU-6L', 'current_quantity': 10, 'unit_price': 450.00},
            {'id': 2, 'name': 'Extincteur CO2 2kg', 'reference': 'EXT-CO2-2KG', 'current_quantity': 5, 'unit_price': 650.00},
            {'id': 3, 'name': 'Extincteur à poudre ABC 9kg', 'reference': 'EXT-ABC-9KG', 'current_quantity': 8, 'unit_price': 550.00}
        ]:
            if p['id'] == id:
                product = p
                break

        if not product:
            product = {'id': id, 'name': f'Produit {id}', 'reference': f'REF-{id}', 'current_quantity': 0, 'unit_price': 0}

        return render_template('pdf/product.html', product=product, company=company)
    elif model_type == 'client':
        # Pour les clients, on peut afficher une fiche client
        client = None
        for c in [
            {'id': 1, 'name': 'Client Test 1', 'address': '123 Rue de Test, Casablanca', 'email': '<EMAIL>', 'phone': '0600000001'},
            {'id': 2, 'name': 'Client Test 2', 'address': '456 Rue de Test, Rabat', 'email': '<EMAIL>', 'phone': '0600000002'},
            {'id': 3, 'name': 'Client Test 3', 'address': '789 Rue de Test, Marrakech', 'email': '<EMAIL>', 'phone': '0600000003'}
        ]:
            if c['id'] == id:
                client = c
                break

        if not client:
            client = {'id': id, 'name': f'Client {id}', 'address': '', 'email': '', 'phone': ''}

        return render_template('pdf/client.html', client=client, company=company)
    else:
        flash('Type de document non pris en charge pour l\'impression.', 'error')
        return redirect(url_for('main.index'))


# Test routes removed

# Demo routes for testing print templates - Cleaned up

# Route pour le design final - Devis
@bp.route('/demo/print/quote-final')
def demo_print_quote_final():
    """Demo print view for final quote design matching the exact model"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('quotes/print_final.html', quote=None, company=company)

# Route pour le design final - Facture
@bp.route('/demo/print/invoice-final')
def demo_print_invoice_final():
    """Demo print view for final invoice design matching the exact model"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('invoices/print_final.html', invoice=None, company=company)

# Route pour le design final - Bon de livraison
@bp.route('/demo/print/delivery-final')
def demo_print_delivery_final():
    """Demo print view for final delivery note design matching the exact model"""
    from app.models.company import Company

    try:
        company = Company.query.first()
    except:
        company = None

    return render_template('delivery_notes/print_final.html', delivery_note=None, company=company)

# Delivery note approval moved to delivery_notes.py to avoid conflicts


