/**
 * RELIABLE CLOSE SYSTEM
 * Simple and guaranteed to work close functionality
 */

// Global close function - works everywhere
function closeWindow() {
    console.log('🚪 Attempting to close window...');
    
    try {
        // Method 1: Try to close popup window
        if (window.opener) {
            console.log('Closing popup window...');
            window.close();
            return;
        }
        
        // Method 2: Try browser back
        if (history.length > 1) {
            console.log('Going back in history...');
            history.back();
            return;
        }
        
        // Method 3: Try to go to parent page
        const currentPath = window.location.pathname;
        let parentPath = '/';
        
        if (currentPath.includes('/quotes/')) {
            parentPath = '/quotes/';
        } else if (currentPath.includes('/invoices/')) {
            parentPath = '/invoices/';
        } else if (currentPath.includes('/delivery_notes/')) {
            parentPath = '/delivery_notes/';
        } else if (currentPath.includes('/clients/')) {
            parentPath = '/clients/';
        } else if (currentPath.includes('/suppliers/')) {
            parentPath = '/suppliers/';
        } else if (currentPath.includes('/products/')) {
            parentPath = '/products/';
        } else if (currentPath.includes('/equipment/')) {
            parentPath = '/equipment/maintenance';
        }
        
        console.log('Redirecting to parent page:', parentPath);
        window.location.href = parentPath;
        
    } catch (error) {
        console.error('Error closing window:', error);
        // Fallback: go to home page
        window.location.href = '/';
    }
}

// Alternative close function with custom redirect
function closeToPage(redirectUrl) {
    console.log('🚪 Closing to specific page:', redirectUrl);
    
    try {
        if (window.opener) {
            window.close();
        } else {
            window.location.href = redirectUrl;
        }
    } catch (error) {
        console.error('Error closing to page:', error);
        window.location.href = redirectUrl;
    }
}

// Initialize close buttons when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚪 Initializing reliable close system...');
    
    // Find and fix all close buttons
    initializeCloseButtons();
    
    // Watch for new buttons added dynamically
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                initializeCloseButtons();
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

function initializeCloseButtons() {
    // Find all potential close buttons
    const closeButtons = document.querySelectorAll(`
        .btn-close,
        .btn-secondary,
        button[onclick*="close"],
        button[onclick*="Close"],
        button[onclick*="fermer"],
        button[onclick*="Fermer"],
        button[title*="Fermer"],
        button[title*="fermer"],
        button[title*="Close"],
        button[title*="close"],
        a[title*="Fermer"],
        a[title*="fermer"],
        [data-bs-dismiss="modal"],
        .close,
        .modal-close
    `);
    
    console.log(`Found ${closeButtons.length} close buttons to fix`);
    
    closeButtons.forEach(button => {
        // Skip if already processed
        if (button.hasAttribute('data-close-processed')) {
            return;
        }
        
        // Mark as processed
        button.setAttribute('data-close-processed', 'true');
        
        // Check if it's a modal close button
        if (button.hasAttribute('data-bs-dismiss') || 
            button.classList.contains('close') || 
            button.classList.contains('modal-close')) {
            // Keep modal functionality
            return;
        }
        
        // Remove any existing event listeners by cloning
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Add new reliable click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Close button clicked');
            
            // Check for custom redirect URL
            const redirectUrl = this.getAttribute('data-redirect-url');
            if (redirectUrl) {
                closeToPage(redirectUrl);
            } else {
                closeWindow();
            }
        });
    });
}

// Handle ESC key for closing
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // Only close if no modal is open
        const openModals = document.querySelectorAll('.modal.show');
        if (openModals.length === 0) {
            console.log('ESC key pressed - closing window');
            closeWindow();
        }
    }
});

// Make functions globally available
window.closeWindow = closeWindow;
window.closeToPage = closeToPage;
window.goBack = closeWindow; // Backward compatibility
window.fermer = closeWindow; // French compatibility

console.log('✅ Reliable close system loaded successfully');
