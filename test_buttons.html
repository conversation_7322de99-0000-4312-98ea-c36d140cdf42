<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Boutons - Gestion d'Extincteurs</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>
            Test des Boutons - Système de Gestion d'Extincteurs
        </h1>

        <!-- Test Delete Buttons -->
        <div class="test-section">
            <h3><i class="fas fa-trash me-2"></i>Test des Boutons de Suppression</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-danger" onclick="deleteItem('Test Item 1', '/test/delete/1')">
                        <i class="fas fa-trash me-1"></i>Supprimer (onclick)
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger" data-delete-url="/test/delete/2" data-item-name="Test Item 2">
                        <i class="fas fa-trash-alt me-1"></i>Supprimer (data-attr)
                    </button>
                </div>
                <div class="col-md-4">
                    <a href="/test/delete/3" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer (link)
                    </a>
                </div>
            </div>
            <div id="deleteTestResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- Test Close Buttons -->
        <div class="test-section">
            <h3><i class="fas fa-times me-2"></i>Test des Boutons de Fermeture</h3>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-secondary" onclick="closeWindow()">
                        <i class="fas fa-times me-1"></i>Fermer (onclick)
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </button>
                </div>
                <div class="col-md-3">
                    <a href="#" class="btn btn-secondary" onclick="goBack()">
                        <i class="fas fa-undo me-1"></i>Annuler
                    </a>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary" onclick="fermer()">
                        <i class="fas fa-door-open me-1"></i>FERMER
                    </button>
                </div>
            </div>
            <div id="closeTestResult" class="test-result" style="display: none;"></div>
        </div>

        <!-- Test Table with Buttons -->
        <div class="test-section">
            <h3><i class="fas fa-table me-2"></i>Test des Boutons dans un Tableau</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Extincteur CO2 5kg</td>
                        <td>Extincteur</td>
                        <td>
                            <button class="btn btn-sm btn-danger" data-delete-url="/products/1/delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="closeWindow()">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>RIA 25mm</td>
                        <td>RIA</td>
                        <td>
                            <a href="/products/2/delete" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Test Modal Buttons -->
        <div class="test-section">
            <h3><i class="fas fa-window-maximize me-2"></i>Test des Boutons de Modal</h3>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                <i class="fas fa-eye me-1"></i>Ouvrir Modal de Test
            </button>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3><i class="fas fa-chart-line me-2"></i>Résultats des Tests</h3>
            <div id="overallResults">
                <p>Cliquez sur les boutons ci-dessus pour tester leur fonctionnement.</p>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Modal de Test</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Ceci est un modal de test pour vérifier les boutons.</p>
                    <form action="/test/modal/delete" method="POST">
                        <button type="button" class="btn btn-danger" onclick="deleteItem('Modal Item', '/test/modal/delete')">
                            <i class="fas fa-trash me-1"></i>Supprimer depuis Modal
                        </button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Fermer
                    </button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Sauvegarder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Test Script -->
    <script>
        console.log('🧪 Starting Button Tests...');

        // Test counter
        let testResults = {
            deleteTests: 0,
            closeTests: 0,
            totalTests: 0,
            passedTests: 0
        };

        // Override functions for testing
        window.originalConfirm = window.confirm;
        window.confirm = function(message) {
            console.log('✅ Confirm dialog called:', message);
            testResults.totalTests++;
            testResults.passedTests++;
            
            // Show test result
            showTestResult('delete', 'Bouton de suppression fonctionne correctement!', true);
            
            return false; // Don't actually delete in test
        };

        window.originalClose = window.close;
        window.close = function() {
            console.log('✅ Window close called');
            testResults.totalTests++;
            testResults.passedTests++;
            showTestResult('close', 'Bouton de fermeture fonctionne correctement!', true);
        };

        // Test functions
        function showTestResult(type, message, success) {
            const resultDiv = document.getElementById(type + 'TestResult');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result ' + (success ? 'test-success' : 'test-error');
                resultDiv.innerHTML = '<i class="fas fa-' + (success ? 'check' : 'times') + ' me-2"></i>' + message;
            }
            
            updateOverallResults();
        }

        function updateOverallResults() {
            const resultsDiv = document.getElementById('overallResults');
            const percentage = testResults.totalTests > 0 ? Math.round((testResults.passedTests / testResults.totalTests) * 100) : 0;
            
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <h5><i class="fas fa-chart-pie me-2"></i>Résultats Globaux</h5>
                    <p><strong>Tests Réussis:</strong> ${testResults.passedTests}/${testResults.totalTests} (${percentage}%)</p>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }

        // Monitor button clicks
        document.addEventListener('click', function(e) {
            if (e.target.matches('.btn-danger, .btn-outline-danger')) {
                console.log('🗑️ Delete button clicked:', e.target);
            }
            
            if (e.target.matches('.btn-secondary')) {
                console.log('🚪 Close button clicked:', e.target);
            }
        });

        console.log('✅ Button test system ready!');
    </script>
</body>
</html>
