/**
 * RELIABLE BUTTONS STYLES
 * Enhanced button styles for better UX
 */

/* Delete Button Styles */
.btn-delete,
.btn-outline-danger {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #dc3545;
    background: transparent;
    color: #dc3545;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 6px;
}

.btn-delete:hover,
.btn-outline-danger:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-delete:active,
.btn-outline-danger:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

/* Close Button Styles */
.btn-close,
.btn-secondary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #6c757d;
    background: transparent;
    color: #6c757d;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 6px;
}

.btn-close:hover,
.btn-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-close:active,
.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
}

/* Primary Button Styles */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #dc3545;
    background: #dc3545;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
}

.btn-primary:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4);
}

/* Success Button Styles */
.btn-success {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #28a745;
    background: #28a745;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
}

.btn-success:hover {
    background: #218838;
    border-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.4);
}

/* Loading State */
.btn-loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Disabled State */
.btn:disabled,
.btn.disabled {
    opacity: 0.5;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
}

/* Button Groups */
.btn-group .btn {
    margin-right: 8px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Responsive Buttons */
@media (max-width: 768px) {
    .btn {
        padding: 12px 16px;
        font-size: 14px;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 12px;
    }

    .btn-lg {
        padding: 16px 24px;
        font-size: 16px;
    }
}

/* Icon Buttons */
.btn i {
    margin-right: 6px;
}

.btn-icon-only {
    padding: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon-only i {
    margin: 0;
}

/* Confirmation Styles */
.btn-confirm {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-confirm:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    transform: scale(1.05);
}

/* Print Button Styles */
.btn-print {
    background: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-print:hover {
    background: #138496;
    border-color: #138496;
    color: white;
}

/* No Print Styles */
@media print {
    .no-print,
    .btn,
    .btn-group {
        display: none !important;
    }
}

/* Focus Styles for Accessibility */
.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn {
        border-width: 3px;
        font-weight: bold;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .btn {
        transition: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* Smart Delete Modal Styles */
#smartDeleteModal .modal-content {
    border: 3px solid #dc3545;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
}

#smartDeleteModal .modal-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

#smartDeleteModal .modal-body {
    padding: 2rem;
}

#smartDeleteModal .modal-footer {
    border-top: 2px solid #dee2e6;
    padding: 1.5rem;
}

#smartDeleteModal .btn {
    min-width: 120px;
    font-weight: 600;
    border-radius: 8px;
    padding: 10px 20px;
}

#smartDeleteModal .btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

#smartDeleteModal .btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.5);
}

#smartDeleteModal .btn-secondary {
    background: #6c757d;
    border: 2px solid #6c757d;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

#smartDeleteModal .btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* COMPLETELY FIXED MODAL SYSTEM - NO MORE BACKDROP ISSUES */
.modal-backdrop {
    display: none !important;
}

.modal {
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
}

.modal-dialog {
    z-index: 10000 !important;
    margin: 1.75rem auto !important;
    pointer-events: auto !important;
    position: relative !important;
}

.modal-content {
    background-color: #fff !important;
    border: 2px solid #dc3545 !important;
    border-radius: 15px !important;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5) !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 10001 !important;
}

.modal-header {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
    border-bottom: none !important;
    border-radius: 13px 13px 0 0 !important;
    padding: 1.5rem !important;
}

.modal-body {
    padding: 2rem !important;
    background: white !important;
    color: #333 !important;
}

.modal-footer {
    background: white !important;
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 13px 13px !important;
    padding: 1.5rem !important;
    display: flex !important;
    justify-content: center !important;
    gap: 1rem !important;
}

.modal-footer .btn {
    min-width: 120px !important;
    font-weight: 600 !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    z-index: 10002 !important;
    position: relative !important;
    transition: all 0.3s ease !important;
}

.modal-footer .btn-secondary {
    background: #6c757d !important;
    color: white !important;
}

.modal-footer .btn-secondary:hover {
    background: #5a6268 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

.modal-footer .btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
    color: white !important;
}

.modal-footer .btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #bd2130) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4) !important;
}

.modal-footer .btn:active {
    transform: translateY(0) !important;
}

/* Fix close button */
.btn-close {
    pointer-events: auto !important;
    z-index: 10003 !important;
    position: relative !important;
    background: none !important;
    border: none !important;
    font-size: 1.5rem !important;
    color: white !important;
    opacity: 0.8 !important;
}

.btn-close:hover {
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* Ensure modal is always on top */
.modal.show {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.modal.show .modal-dialog {
    transform: none !important;
    margin: auto !important;
}
