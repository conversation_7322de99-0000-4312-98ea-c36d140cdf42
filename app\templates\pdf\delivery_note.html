<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> de livraison N° {{ delivery_note.delivery_note_number }}</title>
    <style>
        /* التنسيق العام */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            font-size: 12pt;
            line-height: 1.4;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #17a2b8;
            padding-bottom: 15px;
        }

        .header h2 {
            color: #17a2b8;
            margin: 10px 0;
            font-size: 24pt;
        }

        .logo {
            margin-bottom: 15px;
        }

        .logo img {
            max-height: 80px;
            max-width: 200px;
        }

        .logo-text {
            font-size: 20pt;
            font-weight: bold;
            color: #17a2b8;
            text-transform: uppercase;
        }

        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            gap: 20px;
        }

        .left-section {
            flex: 1;
        }

        .client-info {
            flex: 1;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .client-info h3 {
            color: #17a2b8;
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            font-size: 16pt;
        }

        .info-row {
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 120px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11pt;
        }

        .invoice-table th, .invoice-table td {
            border: 1px solid #333;
            padding: 8px 6px;
            text-align: center;
        }

        .invoice-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
        }

        .invoice-table td.designation {
            text-align: left;
            padding-left: 10px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            gap: 20px;
        }

        .signature-box {
            flex: 1;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            height: 120px;
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            font-weight: bold;
            color: #17a2b8;
        }

        .company-details {
            font-size: 9pt;
            text-align: center;
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            line-height: 1.3;
            color: #666;
        }

        .company-details p {
            margin: 3px 0;
        }

        /* تنسيقات الطباعة */
        @media print {
            body {
                padding: 15px;
                font-size: 11pt;
            }

            .no-print {
                display: none !important;
            }

            .invoice-table {
                page-break-inside: avoid;
            }

            .header, .signature-section, .company-details {
                page-break-inside: avoid;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }

        .empty-row td {
            height: 25px;
            border-color: #ddd;
        }
    </style>
</head>
<body>
    <!-- الترويسة -->
    <div class="header">
        <div class="logo">
            {% if (company and company.logo) or (global_company and global_company.logo) %}
                <img src="{{ url_for('static', filename=(company.logo if company else global_company.logo), _external=True) }}" alt="{{ (company.name if company else global_company.name) }}">
            {% else %}
                <div class="logo-text">MAX AFFAIRE</div>
            {% endif %}
        </div>
        <h2>Bon de livraison N° {{ delivery_note.delivery_note_number }}</h2>
        <p><strong>Date:</strong> {{ delivery_note.date.strftime('%d/%m/%Y') }}</p>
        {% if delivery_note.status %}
        <p><strong>Statut:</strong>
            <span style="font-weight: bold; color: {% if delivery_note.status == 'delivered' %}#28a745{% elif delivery_note.status == 'returned' %}#dc3545{% else %}#ffc107{% endif %};">
                {% if delivery_note.status == 'draft' %}Brouillon
                {% elif delivery_note.status == 'delivered' %}Livré
                {% elif delivery_note.status == 'returned' %}Retourné
                {% endif %}
            </span>
        </p>
        {% endif %}
    </div>

    <!-- معلومات العميل والتسليم -->
    <div class="invoice-info">
        <div class="left-section">
            <div class="info-row">
                <span class="info-label">N° de (Bon de commande Ou marché):</span>
                <span>__________</span>
            </div>
            <div class="info-row">
                <span class="info-label">Objet:</span>
                <span>__________</span>
            </div>
            {% if delivery_note.invoice %}
            <div class="info-row">
                <span class="info-label">Facture liée:</span>
                <span style="font-weight: bold;">{{ delivery_note.invoice.invoice_number }}</span>
            </div>
            {% endif %}
        </div>

        <div class="client-info">
            <h3>Client</h3>
            <p><strong>{{ delivery_note.client.name }}</strong></p>
            {% if delivery_note.client.address %}<p>{{ delivery_note.client.address }}</p>{% endif %}
            {% if delivery_note.client.city %}<p>{{ delivery_note.client.city }}</p>{% endif %}
            {% if delivery_note.client.phone %}<p>Tél: {{ delivery_note.client.phone }}</p>{% endif %}
            {% if delivery_note.client.email %}<p>Email: {{ delivery_note.client.email }}</p>{% endif %}
            {% if delivery_note.client.ice %}<p>ICE: {{ delivery_note.client.ice }}</p>{% endif %}
        </div>
    </div>

    <!-- جدول العناصر -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th style="width: 10%;">N°</th>
                <th style="width: 65%;">Désignation</th>
                <th style="width: 15%;">Unité</th>
                <th style="width: 10%;">QTE</th>
            </tr>
        </thead>
        <tbody>
            {% for item in delivery_note.items %}
            <tr>
                <td>{{ loop.index }}</td>
                <td class="designation">{{ item.product.name }}</td>
                <td>Unité</td>
                <td>{{ item.quantity }}</td>
            </tr>
            {% endfor %}
            <!-- إضافة صفوف فارغة -->
            {% set items_count = delivery_note.items|list|length if delivery_note.items else 0 %}
            {% for i in range(15 - items_count) %}
            <tr class="empty-row">
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- قسم التوقيعات -->
    <div class="signature-section">
        <div class="signature-box">
            <div style="margin-bottom: 15px;">Signature et cachet du client</div>
            <div style="font-size: 10pt; color: #666; margin-top: 40px;">
                Date de réception: ___/___/______
            </div>
        </div>
        <div class="signature-box">
            <div style="margin-bottom: 15px;">Signature et cachet de l'entreprise</div>
            <div style="font-size: 10pt; color: #666; margin-top: 40px;">
                Date de livraison: {{ delivery_note.date.strftime('%d/%m/%Y') }}
            </div>
        </div>
    </div>

    <!-- تفاصيل الشركة -->
    <div class="company-details">
        {% set comp = company if company else global_company %}
        {% if comp and comp.footer_text %}
            {{ comp.footer_text|nl2br }}
        {% else %}
            <p>
                {% if comp %}
                    {{ comp.legal_form if comp.legal_form else "SARL" }} au capital de {{ comp.capital if comp.capital else "2 110 000,00" }} dh - adresse : {{ comp.address if comp.address else "N° 19 , Amal 14 CYM Hay Elkhier Rabat" }} /RC N° : {{ comp.rc if comp.rc else "64133" }} -
                {% else %}
                    SARL au capital de 2 110 000,00 dh - adresse : N° 19 , Amal 14 CYM Hay Elkhier Rabat /RC N° : 64133 -
                {% endif %}
            </p>
            <p>
                {% if comp %}
                    Patente N° {{ comp.patente if comp.patente else "27395222" }} IF :{{ comp.tax_id if comp.tax_id else "3346362" }} - CNSS N° : {{ comp.cnss if comp.cnss else "7304405" }} BANQUE POPULAIRE AGENCE RABAT IBNOU ROCHD compte N °181 810 21211 9001238 000 1 31
                {% else %}
                    Patente N° 27395222 IF :3346362 - CNSS N° : 7304405 BANQUE POPULAIRE AGENCE RABAT IBNOU ROCHD compte N °181 810 21211 9001238 000 1 31
                {% endif %}
            </p>
            <p>
                {% if comp %}
                    Tél: {{ comp.phone if comp.phone else "05 37 29 50 31" }} Fax: {{ comp.fax if comp.fax else "05 37 69 20 86" }} Email:{{ comp.email if comp.email else "<EMAIL>" }} -ICE :{{ comp.ice if comp.ice else "001560529000092" }}
                {% else %}
                    Tél: 05 37 29 50 31 Fax: 05 37 69 20 86 Email:<EMAIL> -ICE :001560529000092
                {% endif %}
            </p>
        {% endif %}
    </div>

    <!-- زر الطباعة (يظهر فقط على الشاشة) -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 12px 24px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14pt; font-weight: bold;">
            <i class="fas fa-print" style="margin-right: 8px;"></i>طباعة بون التسليم
        </button>
        <button onclick="window.close()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14pt; font-weight: bold; margin-left: 10px;">
            <i class="fas fa-times" style="margin-right: 8px;"></i>إغلاق
        </button>
    </div>
</body>
</html>
