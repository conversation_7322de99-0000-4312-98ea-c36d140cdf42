<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de livraison N° {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        /* Header avec logo et titre */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 2px solid #dc3545;
        }

        .logo-section {
            flex: 1;
        }

        .logo-container {
            width: 80px;
            height: 80px;
            border: 2px solid #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            color: #dc3545;
            overflow: hidden;
        }

        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .document-title {
            font-size: 18pt;
            font-weight: bold;
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
        }

        .document-info {
            text-align: right;
            flex: 1;
        }

        .document-info div {
            margin-bottom: 5px;
        }

        /* Section client et informations */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }

        .client-info, .delivery-info {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .client-info h3, .delivery-info h3 {
            margin: 0 0 10px 0;
            color: #dc3545;
            font-size: 12pt;
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
        }

        .client-info p, .delivery-info p {
            margin: 5px 0;
            font-size: 10pt;
        }

        /* Tableau des articles - Version simplifiée pour bon de livraison */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 10pt;
        }

        .items-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 10pt;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section des signatures */
        .signatures-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            gap: 30px;
        }

        .signature-box {
            flex: 1;
            border: 1px solid #ddd;
            height: 100px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 10pt;
        }

        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 9pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="goToDeliveryNotes()">Fermer</button>

        {% if delivery_note %}
            {% if delivery_note.status == 'draft' %}
                <form method="POST" action="{{ url_for('delivery_notes.approve', id=delivery_note.id) }}" style="display: inline;">
                    <button type="submit" class="btn" style="background: #28a745; color: white;"
                            onclick="return confirm('Êtes-vous sûr de vouloir approuver ce bon de livraison? Le stock sera automatiquement mis à jour.')">
                        ✓ Approuver
                    </button>
                </form>
            {% elif delivery_note.status == 'approved' %}
                <span class="btn" style="background: #28a745; color: white; cursor: default;">✓ Approuvé</span>
            {% endif %}
        {% endif %}
    </div>

    <!-- Header avec logo et titre -->
    <div class="header">
        <div class="logo-section">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                {% else %}
                    Logo
                {% endif %}
            </div>
        </div>

        <div class="document-title">BON DE LIVRAISON</div>

        <div class="document-info">
            <div><strong>N° BL:</strong> {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}</div>
            <div><strong>Date:</strong> {{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note and delivery_note.date else '01/01/2024' }}</div>
            {% if delivery_note and delivery_note.status %}
            <div><strong>Statut:</strong>
                <span style="color: {% if delivery_note.status == 'approved' %}#28a745{% elif delivery_note.status == 'draft' %}#ffc107{% else %}#dc3545{% endif %};">
                    {% if delivery_note.status == 'draft' %}Brouillon
                    {% elif delivery_note.status == 'approved' %}Approuvé
                    {% else %}{{ delivery_note.status }}{% endif %}
                </span>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Section client et informations -->
    <div class="info-section">
        <div class="client-info">
            <h3>LIVRER À</h3>
            <p><strong>{{ delivery_note.client.name if delivery_note and delivery_note.client else 'Nom du client' }}</strong></p>
            {% if delivery_note and delivery_note.client %}
                {% if delivery_note.client.address %}<p>{{ delivery_note.client.address }}</p>{% endif %}
                {% if delivery_note.client.city %}<p>{{ delivery_note.client.city }}</p>{% endif %}
                {% if delivery_note.client.phone %}<p>Tél: {{ delivery_note.client.phone }}</p>{% endif %}
                {% if delivery_note.client.email %}<p>Email: {{ delivery_note.client.email }}</p>{% endif %}
                {% if delivery_note.client.ice %}<p>ICE: {{ delivery_note.client.ice }}</p>{% endif %}
            {% endif %}
        </div>

        <div class="delivery-info">
            <h3>INFORMATIONS LIVRAISON</h3>
            <p><strong>Référence:</strong> {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}</p>
            <p><strong>Date livraison:</strong> {{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note and delivery_note.date else '01/01/2024' }}</p>
            {% if delivery_note and delivery_note.invoice %}
            <p><strong>Facture liée:</strong> {{ delivery_note.invoice.invoice_number }}</p>
            {% endif %}
            {% if delivery_note and delivery_note.notes %}
            <p><strong>Notes:</strong> {{ delivery_note.notes }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Tableau des articles - Version simplifiée pour bon de livraison -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 60%;">Description</th>
                <th style="width: 20%;">Unité</th>
                <th style="width: 20%;">Quantité</th>
            </tr>
        </thead>
        <tbody>
            {% if delivery_note and delivery_note.items %}
                {% for item in delivery_note.items %}
                <tr>
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>Unité</td>
                    <td>{{ item.quantity }}</td>
                </tr>
                {% endfor %}
                <!-- Lignes vides pour remplir l'espace -->
                {% set items_count = delivery_note.items|list|length if delivery_note and delivery_note.items else 0 %}
                {% for i in range(10 - items_count) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Lignes vides par défaut -->
                {% for i in range(10) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section des signatures -->
    <div class="signatures-section">
        <div class="signature-box">
            <div>Signature et cachet du client</div>
            <div style="font-size: 9pt; color: #666; margin-top: 60px;">
                Date de réception: ___/___/______
            </div>
        </div>
        <div class="signature-box">
            <div>Signature et cachet de l'entreprise</div>
            <div style="font-size: 9pt; color: #666; margin-top: 60px;">
                Date de livraison: {{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note and delivery_note.date else '___/___/______' }}
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% elif company %}
            {% if company.name %}{{ company.name }}{% endif %}
            {% if company.address %} - {{ company.address }}{% endif %}
            {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
            {% if company.email %} - Email: {{ company.email }}{% endif %}
            {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
        {% endif %}
    </div>

    <script>
        function goToDeliveryNotes() {
            // Try multiple methods to navigate back
            try {
                // Method 1: Go to delivery notes page
                window.location.href = '/commercial/delivery_notes';
            } catch (e) {
                try {
                    // Method 2: Go back in history
                    window.history.back();
                } catch (e2) {
                    try {
                        // Method 3: Close window if it's a popup
                        window.close();
                    } catch (e3) {
                        // Method 4: Fallback to dashboard
                        window.location.href = '/';
                    }
                }
            }
        }
    </script>
</body>
</html>
