from app import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    full_name = db.Column(db.String(100), nullable=True)
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Bo<PERSON>, default=True)

    # Permissions
    can_view_products = db.Column(db.Boolean, default=True)
    can_edit_products = db.Column(db.Boolean, default=False)
    can_view_stock = db.Column(db.<PERSON>, default=True)
    can_edit_stock = db.Column(db.<PERSON>, default=False)
    can_view_clients = db.Column(db.Bo<PERSON>, default=True)
    can_edit_clients = db.Column(db.Boolean, default=False)
    can_view_quotes = db.Column(db.Boolean, default=True)
    can_edit_quotes = db.Column(db.Boolean, default=False)
    can_view_invoices = db.Column(db.Boolean, default=True)
    can_edit_invoices = db.Column(db.Boolean, default=False)
    can_view_delivery_notes = db.Column(db.Boolean, default=True)
    can_edit_delivery_notes = db.Column(db.Boolean, default=False)
    can_print_reports = db.Column(db.Boolean, default=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

    # Password reset fields
    reset_token = db.Column(db.String(100), nullable=True)
    reset_token_expiry = db.Column(db.DateTime, nullable=True)

    def __init__(self, username, email, password, full_name=None, is_admin=False):
        self.username = username
        self.email = email
        self.set_password(password)
        self.full_name = full_name
        self.is_admin = is_admin

        # Admin users get all permissions by default
        if is_admin:
            self.can_edit_products = True
            self.can_edit_stock = True
            self.can_edit_clients = True
            self.can_edit_quotes = True
            self.can_edit_invoices = True
            self.can_edit_delivery_notes = True

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'
