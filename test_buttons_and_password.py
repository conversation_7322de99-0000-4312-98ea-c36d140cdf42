#!/usr/bin/env python3
"""
Test script for button functionality and password reset features
"""

import sys
import os
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_functionality():
    """Test button-related files and functionality"""
    
    print("🔘 Testing Button Functionality...")
    print("=" * 50)
    
    # Test JavaScript files
    js_files = [
        'app/static/js/reliable-delete.js',
        'app/static/js/reliable-close.js'
    ]
    
    for js_file in js_files:
        if os.path.exists(js_file):
            print(f"   ✅ {js_file}")
            # Check file size
            size = os.path.getsize(js_file)
            print(f"      📏 Size: {size} bytes")
        else:
            print(f"   ❌ {js_file} - Missing!")
    
    # Test CSS files
    css_files = [
        'app/static/css/reliable-buttons.css'
    ]
    
    for css_file in css_files:
        if os.path.exists(css_file):
            print(f"   ✅ {css_file}")
            # Check file size
            size = os.path.getsize(css_file)
            print(f"      📏 Size: {size} bytes")
        else:
            print(f"   ❌ {css_file} - Missing!")

def test_password_functionality():
    """Test password reset functionality"""
    
    print("\n🔐 Testing Password Reset Functionality...")
    print("=" * 50)
    
    # Test form files
    form_files = [
        'app/forms/password_reset.py'
    ]
    
    for form_file in form_files:
        if os.path.exists(form_file):
            print(f"   ✅ {form_file}")
        else:
            print(f"   ❌ {form_file} - Missing!")
    
    # Test template files
    template_files = [
        'app/templates/auth/request_password_reset.html',
        'app/templates/auth/reset_password.html',
        'app/templates/auth/change_password.html'
    ]
    
    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"   ✅ {template_file}")
        else:
            print(f"   ❌ {template_file} - Missing!")
    
    # Test route files
    route_files = [
        'app/auth/password_routes.py'
    ]
    
    for route_file in route_files:
        if os.path.exists(route_file):
            print(f"   ✅ {route_file}")
        else:
            print(f"   ❌ {route_file} - Missing!")

def test_database_migration():
    """Test database migration"""
    
    print("\n💾 Testing Database Migration...")
    print("=" * 50)
    
    try:
        import sqlite3
        
        # Database path
        db_path = os.path.join('instance', 'app.db')
        
        if os.path.exists(db_path):
            print(f"   ✅ Database found: {db_path}")
            
            # Check if new columns exist
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'reset_token' in columns:
                print("   ✅ reset_token column exists")
            else:
                print("   ❌ reset_token column missing")
            
            if 'reset_token_expiry' in columns:
                print("   ✅ reset_token_expiry column exists")
            else:
                print("   ❌ reset_token_expiry column missing")
            
            conn.close()
            
        else:
            print(f"   ❌ Database not found: {db_path}")
            
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")

def test_app_integration():
    """Test app integration"""
    
    print("\n🔗 Testing App Integration...")
    print("=" * 50)
    
    try:
        from app import create_app
        from app.models import User
        
        app = create_app()
        print("   ✅ App creation successful")
        
        with app.app_context():
            # Test User model with new fields
            user_count = User.query.count()
            print(f"   📊 Users in database: {user_count}")
            
            # Test if User model has new attributes
            if hasattr(User, 'reset_token'):
                print("   ✅ User.reset_token attribute exists")
            else:
                print("   ❌ User.reset_token attribute missing")
            
            if hasattr(User, 'reset_token_expiry'):
                print("   ✅ User.reset_token_expiry attribute exists")
            else:
                print("   ❌ User.reset_token_expiry attribute missing")
        
        # Test routes
        with app.test_client() as client:
            routes_to_test = [
                ('/auth/request_password_reset', 'Password reset request'),
                ('/auth/change_password', 'Change password (requires login)'),
            ]
            
            for route, description in routes_to_test:
                try:
                    response = client.get(route)
                    if response.status_code in [200, 302]:  # 302 for login redirect
                        status = "✅"
                    else:
                        status = f"❌ ({response.status_code})"
                    print(f"   {status} {description}: {route}")
                except Exception as e:
                    print(f"   ❌ {description}: {route} - Error: {e}")
        
    except Exception as e:
        print(f"   ❌ App integration test failed: {e}")

def test_file_permissions():
    """Test file permissions and accessibility"""
    
    print("\n📁 Testing File Permissions...")
    print("=" * 50)
    
    critical_files = [
        'app/static/js/reliable-delete.js',
        'app/static/js/reliable-close.js',
        'app/static/css/reliable-buttons.css',
        'app/templates/auth/request_password_reset.html',
        'app/templates/auth/reset_password.html',
        'app/templates/auth/change_password.html',
        'app/forms/password_reset.py',
        'app/auth/password_routes.py'
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) > 0:
                        print(f"   ✅ {file_path} - Readable ({len(content)} chars)")
                    else:
                        print(f"   ⚠️  {file_path} - Empty file")
            except Exception as e:
                print(f"   ❌ {file_path} - Read error: {e}")
        else:
            print(f"   ❌ {file_path} - Not found")

if __name__ == '__main__':
    print(f"🧪 Starting comprehensive test at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Testing button functionality and password reset features")
    print("=" * 70)
    
    # Run all tests
    test_button_functionality()
    test_password_functionality()
    test_database_migration()
    test_app_integration()
    test_file_permissions()
    
    print("\n" + "=" * 70)
    print("🎉 COMPREHENSIVE TEST COMPLETE!")
    print("✅ All components have been tested.")
    print("🌐 Access the application at: http://127.0.0.1:5000")
    print("🔐 Try the new password reset features!")
    print("🔘 Test the improved delete and close buttons!")
    print("=" * 70)
    
    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
