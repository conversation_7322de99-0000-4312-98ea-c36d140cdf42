{% extends 'base.html' %}

{% block title %}Modifier <PERSON> {{ delivery_note.delivery_note_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>
            <i class="fas fa-truck me-2"></i>Modifier <PERSON> Livraison {{ delivery_note.delivery_note_number }}
        </h1>
        <div class="mt-2">
            {% if delivery_note.status == 'delivered' %}
                <span class="badge bg-success fs-6">
                    <i class="fas fa-check-circle me-1"></i>Livré - Stock mis à jour
                </span>
            {% elif delivery_note.status == 'draft' %}
                <span class="badge bg-warning fs-6">
                    <i class="fas fa-clock me-1"></i>Brouillon
                </span>
            {% else %}
                <span class="badge bg-info fs-6">
                    <i class="fas fa-hourglass-half me-1"></i>{{ delivery_note.status|title }}
                </span>
            {% endif %}
        </div>
    </div>
    <div>
        {% if delivery_note.status != 'delivered' %}
        <form action="{{ url_for('main.commercial_delivery_notes_approve', id=delivery_note.id) }}" method="post" class="d-inline me-2">
            <button type="submit" class="btn btn-success" onclick="return confirm('Confirmer la livraison et mettre à jour le stock automatiquement ?')">
                <i class="fas fa-check-circle me-1"></i>Confirmer Livraison
            </button>
        </form>
        {% endif %}
        <a href="{{ url_for('delivery_notes.show', id=delivery_note.id) }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('delivery_notes.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations du bon de livraison</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('delivery_notes.edit', id=delivery_note.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        <label for="client_id" class="form-label required">Client</label>
                        {{ form.client_id(class="form-select") }}
                        {% if form.client_id.errors %}
                            <div class="text-danger">
                                {% for error in form.client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label required">Statut</label>
                        {{ form.status(class="form-select") }}
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% if form.date.errors %}
                            <div class="text-danger">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        {{ form.notes(class="form-control", rows=3) }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Produits</h5>
                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <i class="fas fa-plus-circle me-1"></i>Ajouter un produit
                </button>
            </div>
            <div class="card-body">
                {% if delivery_note.items.all() %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Référence</th>
                                <th>Quantité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in delivery_note.items %}
                            <tr>
                                <td>{{ item.product.name }}</td>
                                <td>{{ item.product.reference }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>
                                    <form action="{{ url_for('delivery_notes.remove_item', delivery_note_id=delivery_note.id, item_id=item.id) }}" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article ?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Aucun produit n'a été ajouté à ce bon de livraison.
                    <button type="button" class="btn btn-sm btn-success ms-2" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus-circle me-1"></i>Ajouter un produit
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un produit -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{{ url_for('delivery_notes.add_item', id=delivery_note.id) }}">
                {{ item_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="product_id" class="form-label required">Produit</label>
                        {{ item_form.product_id(class="form-select") }}
                        {% if item_form.product_id.errors %}
                            <div class="text-danger">
                                {% for error in item_form.product_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="quantity" class="form-label required">Quantité</label>
                        {{ item_form.quantity(class="form-control", value="1") }}
                        {% if item_form.quantity.errors %}
                            <div class="text-danger">
                                {% for error in item_form.quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    {{ item_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
