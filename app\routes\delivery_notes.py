from flask import Blueprint, render_template, redirect, url_for, flash, request
from app import db
from app.models import DeliveryNote, DeliveryNoteItem, Product, Client, Invoice, InvoiceItem, StockMovement
from app.forms.delivery_note_forms import DeliveryNoteForm, DeliveryNoteItemForm
from datetime import datetime
import random
import string

bp = Blueprint('delivery_notes', __name__)

def approve_delivery_note_and_update_stock(delivery_note_id):
    """Approve delivery note and automatically update stock"""
    delivery_note = DeliveryNote.query.get(delivery_note_id)
    if not delivery_note:
        return False, "Bon de livraison introuvable"

    if delivery_note.status == 'delivered':
        return False, "Bon de livraison déjà livré"

    try:
        # Update delivery note status
        delivery_note.status = 'delivered'

        # Create stock movements for each item
        for item in delivery_note.items:
            if item.product_id and item.quantity > 0:
                product = Product.query.get(item.product_id)
                if product:
                    # Check if enough stock available
                    if product.current_quantity < item.quantity:
                        return False, f"Stock insuffisant pour {product.name}. Disponible: {product.current_quantity}, Demandé: {item.quantity}"

                    # Create stock movement
                    movement = StockMovement(
                        product_id=item.product_id,
                        movement_type='out',
                        quantity=item.quantity,
                        reference_document=delivery_note.delivery_note_number,
                        delivery_note_id=delivery_note.id,
                        notes=f"Sortie automatique - Livraison {delivery_note.delivery_note_number}"
                    )
                    db.session.add(movement)

                    # Update product quantity
                    product.current_quantity -= item.quantity

        db.session.commit()
        return True, f"Bon de livraison approuvé et stock mis à jour"

    except Exception as e:
        db.session.rollback()
        return False, f"Erreur lors de la mise à jour: {str(e)}"

def generate_delivery_note_number():
    prefix = "BL-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

@bp.route('/')
def index():
    delivery_notes = DeliveryNote.query.order_by(DeliveryNote.date.desc()).all()
    return render_template('delivery_notes/index.html', delivery_notes=delivery_notes)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    form = DeliveryNoteForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    # Populate invoice choices (optional)
    invoices = Invoice.query.filter_by(status='paid').all()
    form.invoice_id.choices = [(0, 'None')] + [(i.id, f"{i.invoice_number} - {i.client.name}") for i in invoices]

    if form.validate_on_submit():
        delivery_note = DeliveryNote(
            delivery_note_number=generate_delivery_note_number(),
            client_id=form.client_id.data,
            date=form.date.data or datetime.utcnow(),
            status=form.status.data,
            notes=form.notes.data
        )

        if form.invoice_id.data and form.invoice_id.data > 0:
            delivery_note.invoice_id = form.invoice_id.data
            invoice = Invoice.query.get(form.invoice_id.data)

            # Copy items from invoice
            for invoice_item in invoice.items:
                delivery_note_item = DeliveryNoteItem(
                    product_id=invoice_item.product_id,
                    quantity=invoice_item.quantity
                )
                delivery_note.items.append(delivery_note_item)

        db.session.add(delivery_note)
        db.session.commit()

        flash('Delivery note created successfully!', 'success')
        return redirect(url_for('delivery_notes.edit', id=delivery_note.id))

    # Get company information for the header
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('delivery_notes/create.html', form=form, company=company)

@bp.route('/create-interactive', methods=['GET', 'POST'])
def create_interactive():
    """Interactive delivery note creation with document-like interface"""
    from datetime import datetime, timedelta
    import json

    if request.method == 'POST':
        # Handle AJAX request for saving delivery note
        try:
            data = request.get_json()

            # Create new delivery note
            delivery_note = DeliveryNote(
                delivery_note_number=generate_delivery_note_number(),
                client_id=data.get('client_id'),
                date=datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else datetime.utcnow(),
                delivery_date=datetime.strptime(data.get('delivery_date'), '%Y-%m-%d') if data.get('delivery_date') else datetime.utcnow(),
                status='draft',
                notes=data.get('object', '')
            )

            db.session.add(delivery_note)
            db.session.flush()  # Get the delivery note ID

            # Add items
            for item_data in data.get('items', []):
                item = DeliveryNoteItem(
                    delivery_note_id=delivery_note.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity')
                )
                db.session.add(item)

            db.session.commit()

            return jsonify({'success': True, 'delivery_note_id': delivery_note.id})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    # Generate delivery note number and dates
    delivery_number = generate_delivery_note_number()
    today = datetime.now().strftime('%Y-%m-%d')
    delivery_date = datetime.now().strftime('%Y-%m-%d')

    return render_template('delivery_notes/create_interactive.html',
                         clients=clients,
                         company=company,
                         delivery_number=delivery_number,
                         today=today,
                         delivery_date=delivery_date)

@bp.route('/<int:id>/edit-interactive', methods=['GET', 'POST'])
def edit_interactive(id):
    """Interactive delivery note editing with document-like interface"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    if request.method == 'POST':
        # Handle AJAX request for updating delivery note
        try:
            data = request.get_json()

            # Update delivery note
            delivery_note.client_id = data.get('client_id')
            delivery_note.date = datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else delivery_note.date
            delivery_note.delivery_date = datetime.strptime(data.get('delivery_date'), '%Y-%m-%d') if data.get('delivery_date') else delivery_note.delivery_date
            delivery_note.notes = data.get('object', '')

            # Delete existing items
            DeliveryNoteItem.query.filter_by(delivery_note_id=delivery_note.id).delete()

            # Add new items
            for item_data in data.get('items', []):
                item = DeliveryNoteItem(
                    delivery_note_id=delivery_note.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity')
                )
                db.session.add(item)

            db.session.commit()

            return jsonify({'success': True, 'delivery_note_id': delivery_note.id})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('delivery_notes/edit_interactive.html',
                         delivery_note=delivery_note,
                         clients=clients,
                         company=company)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    delivery_note = DeliveryNote.query.get_or_404(id)
    form = DeliveryNoteForm(obj=delivery_note)
    item_form = DeliveryNoteItemForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    # Populate invoice choices (optional)
    invoices = Invoice.query.filter_by(status='paid').all()
    form.invoice_id.choices = [(0, 'None')] + [(i.id, f"{i.invoice_number} - {i.client.name}") for i in invoices]

    # Populate product choices for the item form
    item_form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        delivery_note.client_id = form.client_id.data
        delivery_note.date = form.date.data
        delivery_note.status = form.status.data
        delivery_note.notes = form.notes.data

        db.session.commit()

        flash('Delivery note updated successfully!', 'success')
        return redirect(url_for('delivery_notes.edit', id=delivery_note.id))

    return render_template('delivery_notes/edit.html', delivery_note=delivery_note, form=form, item_form=item_form)

@bp.route('/<int:id>/add_item', methods=['POST'])
def add_item(id):
    delivery_note = DeliveryNote.query.get_or_404(id)
    form = DeliveryNoteItemForm()

    # Populate product choices
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        product = Product.query.get(form.product_id.data)

        item = DeliveryNoteItem(
            delivery_note_id=delivery_note.id,
            product_id=form.product_id.data,
            quantity=form.quantity.data
        )

        db.session.add(item)
        db.session.commit()

        flash('Item added to delivery note!', 'success')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"{getattr(form, field).label.text}: {error}", 'danger')

    return redirect(url_for('delivery_notes.edit', id=delivery_note.id))

@bp.route('/<int:delivery_note_id>/remove_item/<int:item_id>', methods=['POST'])
def remove_item(delivery_note_id, item_id):
    item = DeliveryNoteItem.query.get_or_404(item_id)

    db.session.delete(item)
    db.session.commit()

    flash('Item removed from delivery note!', 'success')
    return redirect(url_for('delivery_notes.edit', id=delivery_note_id))

@bp.route('/<int:id>')
def show(id):
    """Show delivery note using the same interactive template as create/edit"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    try:
        from app.models.company import Company
        from app.models import Client

        # Get company information
        company = Company.query.first()

        # Get all clients for the dropdown
        clients = Client.query.all()

        # Render the show template with the same design as create/edit
        return render_template('delivery_notes/show.html',
                             delivery_note=delivery_note,
                             company=company,
                             clients=clients)
    except Exception as e:
        flash(f'Erreur lors de l\'affichage du bon de livraison: {str(e)}', 'danger')
        return redirect(url_for('delivery_notes.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    delivery_note = DeliveryNote.query.get_or_404(id)
    db.session.delete(delivery_note)
    db.session.commit()
    flash('Delivery note deleted successfully!', 'success')
    return redirect(url_for('delivery_notes.index'))

@bp.route('/<int:id>/generate_pdf')
def generate_pdf(id):
    """Display HTML template for printing (same as create form)"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    try:
        from app.models.company import Company

        # Get company information
        company = Company.query.first()

        # Render the PDF template with the same design as create/edit
        return render_template('delivery_notes/generate_pdf.html', delivery_note=delivery_note, company=company)

    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('delivery_notes.show', id=delivery_note.id))

@bp.route('/<int:id>/view_pdf')
def view_pdf(id):
    """View PDF template in browser"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    try:
        from app.models.company import Company
        company = Company.query.first()
        return render_template('delivery_notes/generate_pdf.html', delivery_note=delivery_note, company=company)
    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('delivery_notes.show', id=delivery_note.id))

@bp.route('/<int:id>/approve', methods=['POST'])
def approve(id):
    """Approve delivery note and update stock automatically"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    if delivery_note.status == 'delivered':
        flash('Bon de livraison déjà livré', 'info')
        return redirect(url_for('delivery_notes.edit', id=id))

    try:
        # Update delivery note status
        delivery_note.status = 'delivered'

        # Update stock for each item
        for item in delivery_note.items:
            if item.product_id and item.quantity > 0:
                product = Product.query.get(item.product_id)
                if product:
                    # Check if enough stock available
                    if product.current_quantity < item.quantity:
                        flash(f'Stock insuffisant pour {product.name}. Disponible: {product.current_quantity}, Demandé: {item.quantity}', 'error')
                        return redirect(url_for('delivery_notes.edit', id=id))

                    # Update product quantity
                    product.current_quantity -= item.quantity

        db.session.commit()
        flash(f'Bon de livraison {delivery_note.delivery_note_number} approuvé avec succès! Le stock a été mis à jour automatiquement.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'approbation: {str(e)}', 'error')

    return redirect(url_for('delivery_notes.edit', id=id))

@bp.route('/<int:id>/cancel_approval', methods=['POST'])
def cancel_approval(id):
    """Cancel approval and reverse stock movements"""
    delivery_note = DeliveryNote.query.get_or_404(id)

    try:
        if delivery_note.status != 'approved':
            flash('Ce bon de livraison n\'est pas approuvé.', 'warning')
            return redirect(url_for('delivery_notes.show', id=delivery_note.id))

        # Reverse stock movements
        for movement in delivery_note.stock_movements:
            if movement.movement_type == 'OUT':
                # Add back to stock
                product = Product.query.get(movement.product_id)
                if product:
                    product.current_quantity += movement.quantity

                # Delete the movement
                db.session.delete(movement)

        # Update status back to draft
        delivery_note.status = 'draft'
        db.session.commit()

        flash(f'Approbation annulée pour le bon de livraison {delivery_note.delivery_note_number}. Le stock a été restauré.', 'success')
        return redirect(url_for('delivery_notes.show', id=delivery_note.id))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'annulation: {str(e)}', 'danger')
        return redirect(url_for('delivery_notes.show', id=delivery_note.id))
