<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de livraison N° {{ delivery_note.delivery_number if delivery_note else 'BL-202508-5412' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        /* Header rouge simple */
        .header {
            background-color: #dc3545;
            color: white;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            margin-bottom: 20px;
        }

        .logo-section {
            width: 80px;
            height: 50px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            border-radius: 3px;
        }

        .logo-section img {
            max-width: 70px;
            max-height: 40px;
            object-fit: contain;
        }

        .logo-placeholder {
            color: #dc3545;
            font-size: 8pt;
            font-weight: bold;
            text-align: center;
        }

        .document-title {
            font-size: 28pt;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0;
        }

        /* Section Client simple */
        .client-section {
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
            gap: 40px;
        }

        .client-info {
            flex: 1;
        }

        .document-info {
            flex: 1;
            text-align: left;
        }

        .info-line {
            margin-bottom: 3px;
            font-size: 11pt;
        }

        .info-label {
            color: #333;
            font-weight: bold;
            display: inline-block;
            min-width: 140px;
        }

        .info-value {
            color: #333;
        }

        /* Section Objet simple */
        .object-section {
            padding: 0 20px;
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            font-size: 12pt;
        }

        .object-content {
            font-size: 11pt;
            color: #333;
            padding: 5px 0;
        }

        /* Tableau des articles - Version simplifiée pour bon de livraison */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 20px 20px 20px;
            width: calc(100% - 40px);
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 10px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 25px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background-color: white;
        }

        .description-col {
            text-align: left !important;
            padding-left: 10px !important;
        }

        /* Section des signatures */
        .signatures-section {
            margin: 40px 20px 20px 20px;
            display: flex;
            justify-content: space-between;
            gap: 30px;
        }

        .signature-box {
            flex: 1;
            border: 1px solid #ddd;
            height: 100px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 11pt;
            display: flex;
            align-items: flex-end;
            justify-content: center;
        }

        /* Footer simple */
        .footer {
            margin-top: 40px;
            padding: 15px 20px;
            text-align: center;
            font-size: 9pt;
            color: #666;
            border-top: 1px solid #ddd;
            line-height: 1.3;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        .btn-approve {
            background: #28a745;
            color: white;
        }

        .btn-cancel {
            background: #ffc107;
            color: black;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4 portrait;
                margin: 15mm;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
                font-size: 11pt !important;
                background: white !important;
            }

            .header {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .items-table th {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .items-table thead {
                display: table-header-group !important;
            }

            .items-table tbody {
                display: table-row-group !important;
            }

            .footer {
                page-break-inside: avoid !important;
            }

            .signatures-section {
                page-break-inside: avoid !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="goToDeliveryNotes()">Fermer</button>

        {% if delivery_note %}
            {% if delivery_note.status == 'draft' %}
                <form method="POST" action="{{ url_for('delivery_notes.approve', id=delivery_note.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-approve"
                            onclick="return confirm('Êtes-vous sûr de vouloir approuver ce bon de livraison? Le stock sera automatiquement mis à jour.')">
                        ✓ Approuver
                    </button>
                </form>
            {% elif delivery_note.status == 'approved' %}
                <span class="btn btn-approve" style="cursor: default;">✓ Approuvé</span>
                <form method="POST" action="{{ url_for('delivery_notes.cancel_approval', id=delivery_note.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-cancel"
                            onclick="return confirm('Êtes-vous sûr de vouloir annuler l\'approbation? Le stock sera restauré.')">
                        ↶ Annuler
                    </button>
                </form>
            {% endif %}
        {% endif %}
    </div>

    <!-- Header rouge simple -->
    <div class="header">
        <div class="logo-section">
            {% if company and company.logo %}
                <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
            {% else %}
                <div class="logo-placeholder">LOGO<br>ENTREPRISE</div>
            {% endif %}
        </div>
        <div class="document-title">Bon de livraison</div>
    </div>

    <!-- Section Client simple -->
    <div class="client-section">
        <div class="client-title">Client :</div>
        <div class="client-details">
            <div class="client-info">
                <div class="info-line">
                    <span class="info-label">Nom du client :</span>
                    <span class="info-value">{{ delivery_note.client.name if delivery_note and delivery_note.client else 'Société ABC SARL' }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Ice :</span>
                    <span class="info-value">{{ delivery_note.client.ice if delivery_note and delivery_note.client and delivery_note.client.ice else '1234455' }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone :</span>
                    <span class="info-value">{{ delivery_note.client.phone if delivery_note and delivery_note.client else '+212 522 123 456' }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Email :</span>
                    <span class="info-value">{{ delivery_note.client.email if delivery_note and delivery_note.client else '<EMAIL>' }}</span>
                </div>
            </div>
            <div class="document-info">
                <div class="info-line">
                    <span class="info-label">Date du BL :</span>
                    <span class="info-value">{{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note and delivery_note.date else '24/08/2025' }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Référence du BL :</span>
                    <span class="info-value">{{ delivery_note.delivery_number if delivery_note else 'BL-202508-5412' }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Date de livraison :</span>
                    <span class="info-value">{{ delivery_note.delivery_date.strftime('%d/%m/%Y') if delivery_note and delivery_note.delivery_date else '23/09/2025' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Objet -->
    <div class="object-section">
        <div class="object-title">Objet :</div>
        <div class="object-content">{{ delivery_note.notes if delivery_note and delivery_note.notes else 'Livraison de produits' }}</div>
    </div>

    <!-- Tableau des articles - Version simplifiée pour bon de livraison -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 60%;">Description</th>
                <th style="width: 20%;">Unité</th>
                <th style="width: 20%;">Quantité</th>
            </tr>
        </thead>
        <tbody>
            {% if delivery_note and delivery_note.items %}
                {% for item in delivery_note.items %}
                <tr>
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>Unité</td>
                    <td>{{ item.quantity }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Exemple par défaut -->
                <tr>
                    <td class="description-col">Co2 2k</td>
                    <td>Unité</td>
                    <td>2</td>
                </tr>
            {% endif %}
        </tbody>
    </table>

    <!-- Section des signatures -->
    <div class="signatures-section">
        <div class="signature-box">
            <div>Signature et cachet du client</div>
        </div>
        <div class="signature-box">
            <div>Signature et cachet de l'entreprise</div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% else %}
            SARL au capital de 2 100 000,00 dh - adresse : N° 49 , Amal 14 CYM Hay Elbhar Rabat RC N° : 64133 - Patente N° 27398229 IF 3448343 - CNSS N° 7754804 BANQUE POPULAIRE AGENCE RABAT CENTRE
            2141 9001238 000 1 31 Tel: 05 37 29 50 31 Fax: 05 37 69 20 86 Email:<EMAIL> -ICE 000958220000082
        {% endif %}
    </div>

    <script>
        function goToDeliveryNotes() {
            try {
                window.location.href = '/commercial/delivery_notes';
            } catch (e) {
                try {
                    window.history.back();
                } catch (e2) {
                    try {
                        window.close();
                    } catch (e3) {
                        window.location.href = '/';
                    }
                }
            }
        }
    </script>
</body>
</html>
