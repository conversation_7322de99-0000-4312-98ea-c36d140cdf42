/* ===== FIREGUARD PRO - MODERN UI SYSTEM ===== */

:root {
    /* Primary Colors */
    --primary-color: #dc3545;
    --primary-hover: #c82333;
    --primary-light: #f8d7da;
    --primary-dark: #a71e2a;

    /* Secondary Colors */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --danger-color: #dc3545;

    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #495057;
    --black: #212529;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    --gradient-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-pill: 50rem;

    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --shadow-xl: 0 2rem 4rem rgba(0, 0, 0, 0.2);

    /* Transitions */
    --transition-fast: all 0.15s ease-in-out;
    --transition-normal: all 0.3s ease-in-out;
    --transition-slow: all 0.5s ease-in-out;

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    min-height: 100vh;
    font-family: var(--font-family-primary);
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.6;
    color: var(--dark-gray);
    background: var(--gradient-background);
    background-attachment: fixed;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===== MODERN NAVIGATION ===== */
.modern-navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(220, 53, 69, 0.1);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-sm) 0;
    transition: var(--transition-normal);
    position: sticky;
    top: 0;
    z-index: var(--z-fixed);
}

.modern-navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-lg);
}

/* Brand Styling */
.modern-brand {
    text-decoration: none !important;
    color: var(--primary-color) !important;
    transition: var(--transition-normal);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-title {
    font-family: var(--font-family-heading);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.brand-subtitle {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-brand:hover .brand-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* Modern Toggler */
.modern-toggler {
    border: none;
    padding: var(--spacing-xs);
    width: 40px;
    height: 40px;
    position: relative;
    background: transparent;
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
}

.modern-toggler:focus {
    box-shadow: none;
}

.modern-toggler span {
    display: block;
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 5px auto;
    border-radius: var(--border-radius-pill);
    transition: var(--transition-fast);
}

.modern-toggler:hover span {
    background: var(--primary-hover);
}

/* Navigation Links */
.modern-nav-link {
    color: var(--dark-gray) !important;
    font-weight: 500;
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--border-radius-md);
    margin: 0 var(--spacing-xs);
    transition: var(--transition-normal);
    position: relative;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.modern-nav-link i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.modern-nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(220, 53, 69, 0.1);
    transform: translateY(-1px);
}

.modern-nav-link.active {
    color: var(--primary-color) !important;
    background: var(--primary-light);
    font-weight: 600;
}

/* Modern Dropdowns */
.modern-dropdown-menu {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    min-width: 220px;
    z-index: var(--z-dropdown);
}

.modern-dropdown-item {
    color: var(--dark-gray) !important;
    font-weight: 500;
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xs);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    text-decoration: none;
}

.modern-dropdown-item:last-child {
    margin-bottom: 0;
}

.modern-dropdown-item i {
    font-size: 0.875rem;
    width: 20px;
    text-align: center;
    opacity: 0.7;
}

.modern-dropdown-item:hover {
    color: var(--white) !important;
    background: var(--gradient-primary);
    transform: translateX(5px);
}

.modern-dropdown-item:hover i {
    opacity: 1;
}

.modern-dropdown-item.text-danger:hover {
    background: var(--gradient-primary);
    color: var(--white) !important;
}

/* ===== MAIN CONTENT AREA ===== */
.main-content {
    flex: 1;
    min-height: calc(100vh - 140px);
    padding-top: var(--spacing-md);
}

.page-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

/* ===== ENHANCED FLASH MESSAGES ===== */
.flash-messages-container {
    position: fixed;
    top: 80px;
    right: var(--spacing-lg);
    z-index: var(--z-tooltip);
    max-width: 400px;
    width: 100%;
}

.modern-alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.modern-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.alert-message {
    flex: 1;
    font-weight: 500;
    font-size: 0.875rem;
}

.modern-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    opacity: 0.7;
    transition: var(--transition-fast);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
}

.modern-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
}

/* FIXED ALERT COLORS */
.alert-success, .modern-alert.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15), rgba(40, 167, 69, 0.05)) !important;
    color: #155724 !important;
    border: 1px solid rgba(40, 167, 69, 0.3) !important;
    border-left: 4px solid #28a745 !important;
}

.alert-danger, .alert-error, .modern-alert.alert-danger, .modern-alert.alert-error {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.15), rgba(220, 53, 69, 0.05)) !important;
    color: #721c24 !important;
    border: 1px solid rgba(220, 53, 69, 0.3) !important;
    border-left: 4px solid #dc3545 !important;
}

.alert-warning, .modern-alert.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.05)) !important;
    color: #856404 !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
    border-left: 4px solid #ffc107 !important;
}

.alert-info, .modern-alert.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.15), rgba(23, 162, 184, 0.05)) !important;
    color: #0c5460 !important;
    border: 1px solid rgba(23, 162, 184, 0.3) !important;
    border-left: 4px solid #17a2b8 !important;
}

/* Ensure text is always readable */
.alert-success .alert-message,
.alert-danger .alert-message,
.alert-error .alert-message,
.alert-warning .alert-message,
.alert-info .alert-message {
    color: inherit !important;
    font-weight: 500 !important;
}

/* Fix icon colors */
.alert-success .alert-icon {
    color: #28a745 !important;
}

.alert-danger .alert-icon,
.alert-error .alert-icon {
    color: #dc3545 !important;
}

.alert-warning .alert-icon {
    color: #ffc107 !important;
}

.alert-info .alert-icon {
    color: #17a2b8 !important;
}

/* ===== MODERN FOOTER ===== */
.modern-footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(220, 53, 69, 0.1);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.footer-brand {
    display: flex;
    align-items: center;
    font-family: var(--font-family-heading);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color);
}

.footer-description {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin: 0;
}

.footer-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.footer-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-copyright {
    font-size: 0.75rem;
    color: var(--secondary-color);
    text-align: right;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-right {
        align-items: center;
    }

    .footer-copyright {
        text-align: center;
    }
}

/* ===== DASHBOARD STYLES ===== */
.dashboard-header {
    margin-bottom: var(--spacing-xxl);
}

.dashboard-title {
    font-family: var(--font-family-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: var(--spacing-sm);
}

.dashboard-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-md);
    font-weight: 400;
}

.header-actions .modern-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.header-actions .modern-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== ENHANCED STATS CARDS ===== */
.stats-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(20px);
    height: 100%;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(220, 53, 69, 0.3);
}

.stats-card:hover::before {
    height: 6px;
}

.stats-card-body {
    padding: var(--spacing-xl);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stats-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex: 1;
    margin-bottom: var(--spacing-md);
}

.stats-info {
    flex: 1;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
}

.stats-number {
    font-family: var(--font-family-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stats-description {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin: 0;
    font-weight: 500;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    background: var(--gradient-primary);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.stats-card:hover .stats-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.stats-footer {
    margin-top: auto;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--medium-gray);
}

.stats-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.stats-link:hover {
    color: var(--primary-hover);
    transform: translateX(5px);
}

/* Stats Card Variants */
.stats-card-primary::before {
    background: var(--gradient-primary);
}

.stats-card-primary .stats-icon {
    background: var(--gradient-primary);
}

.stats-card-success::before {
    background: linear-gradient(135deg, var(--success-color), #218838);
}

.stats-card-success .stats-icon {
    background: linear-gradient(135deg, var(--success-color), #218838);
}

.stats-card-warning::before {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
}

.stats-card-warning .stats-icon {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
}

.stats-card-info::before {
    background: linear-gradient(135deg, var(--info-color), #138496);
}

.stats-card-info .stats-icon {
    background: linear-gradient(135deg, var(--info-color), #138496);
}

.stats-card-secondary::before {
    background: var(--gradient-secondary);
}

.stats-card-secondary .stats-icon {
    background: var(--gradient-secondary);
}

/* ===== MODAL FIXES ===== */
.modal-backdrop {
    opacity: 0.5 !important;
    z-index: var(--z-modal-backdrop) !important;
}

.modal {
    z-index: var(--z-modal) !important;
}

.modal-dialog {
    z-index: calc(var(--z-modal) + 1) !important;
}

/* Fix for modal interaction issues */
.modal.show {
    display: block !important;
}

.modal-backdrop.show {
    opacity: 0.5 !important;
}

/* Ensure buttons work in modals */
.modal .btn {
    z-index: calc(var(--z-modal) + 2) !important;
    position: relative;
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: var(--transition-normal);
    border: none;
    position: relative;
    z-index: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
    color: #fff !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #218838);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* Unified Delete Button Style - Simple and Working */
.btn-delete, .btn-outline-danger, .btn-delete-modern, .btn-delete-glow,
.btn-delete-neon, .btn-delete-3d, .btn-delete-text {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border: 2px solid #dc3545 !important;
    color: white !important;
    font-weight: 600;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-delete:hover, .btn-outline-danger:hover, .btn-delete-modern:hover,
.btn-delete-glow:hover, .btn-delete-neon:hover, .btn-delete-3d:hover, .btn-delete-text:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    border-color: #c82333 !important;
}

.btn-delete:active, .btn-outline-danger:active, .btn-delete-modern:active,
.btn-delete-glow:active, .btn-delete-neon:active, .btn-delete-3d:active, .btn-delete-text:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.btn-delete i, .btn-outline-danger i, .btn-delete-modern i,
.btn-delete-glow i, .btn-delete-neon i, .btn-delete-3d i, .btn-delete-text i {
    font-size: 14px;
    margin-right: 4px;
}

/* Small delete buttons */
.btn-sm.btn-delete, .btn-sm.btn-outline-danger, .btn-sm.btn-delete-modern,
.btn-sm.btn-delete-glow, .btn-sm.btn-delete-neon, .btn-sm.btn-delete-3d, .btn-sm.btn-delete-text {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-sm.btn-delete i, .btn-sm.btn-outline-danger i, .btn-sm.btn-delete-modern i,
.btn-sm.btn-delete-glow i, .btn-sm.btn-delete-neon i, .btn-sm.btn-delete-3d i, .btn-sm.btn-delete-text i {
    font-size: 12px;
    margin-right: 2px;
}

/* Responsive delete buttons */
@media (max-width: 768px) {
    .btn-delete, .btn-outline-danger, .btn-delete-modern,
    .btn-delete-glow, .btn-delete-neon, .btn-delete-3d, .btn-delete-text {
        padding: 6px 8px;
        font-size: 12px;
    }

    .btn-delete i, .btn-outline-danger i, .btn-delete-modern i,
    .btn-delete-glow i, .btn-delete-neon i, .btn-delete-3d i, .btn-delete-text i {
        font-size: 12px;
        margin-right: 2px;
    }
}

/* Accessibility improvements */
.btn-delete:focus, .btn-outline-danger:focus, .btn-delete-modern:focus,
.btn-delete-glow:focus, .btn-delete-neon:focus, .btn-delete-3d:focus, .btn-delete-text:focus {
    outline: 3px solid rgba(255, 71, 87, 0.5);
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .btn-delete-neon {
        border-color: #ff4757;
        color: #ff4757;
    }

    .btn-delete-neon:hover {
        background: #ff4757;
        color: #000;
    }
}

.text-primary {
    color: #dc3545 !important;
}

.badge-info {
    background-color: #dc3545 !important;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: #343a40 !important;
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(220, 53, 69, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
    z-index: 9999 !important;
    position: absolute !important;
}

/* Fix for all dropdown menus */
.dropdown,
.nav-item.dropdown,
.btn-group {
    position: relative;
    z-index: 1000;
}

.dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 160px;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-menu.show {
    z-index: 9999 !important;
    position: absolute !important;
    display: block !important;
}

/* Ensure navbar dropdowns work */
.navbar .dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
}

/* Fix for select elements */
select.form-select,
select.form-control {
    z-index: 1000 !important;
    position: relative !important;
}

/* Fix for cards and containers that might interfere */
.card {
    z-index: 1;
    position: relative;
}

.container,
.container-fluid {
    z-index: 1;
    position: relative;
}

/* Specific fix for maintenance page dropdowns */
.maintenance-filters .dropdown-menu {
    z-index: 10000 !important;
    position: absolute !important;
    background: white !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Force all dropdowns to appear above everything - but only when shown */
.dropdown-menu.show,
.nav-item .dropdown-menu.show,
.navbar .dropdown-menu.show,
.btn-group .dropdown-menu.show {
    z-index: 99999 !important;
    position: absolute !important;
    background-color: #ffffff !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    min-width: 160px !important;
    padding: 0.5rem 0 !important;
    margin: 0.125rem 0 0 !important;
    font-size: 0.875rem !important;
    color: #212529 !important;
    text-align: left !important;
    list-style: none !important;
    background-clip: padding-box !important;
    display: block !important;
}

/* Ensure dropdown items are visible */
.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.25rem 1rem !important;
    clear: both !important;
    font-weight: 400 !important;
    color: #212529 !important;
    text-align: inherit !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    z-index: 99999 !important;
}

/* Override any conflicting styles */
.card,
.card-body,
.container,
.container-fluid,
.row,
.col,
.table-responsive {
    z-index: auto !important;
}

/* Specific override for navbar */
.navbar {
    z-index: 1030 !important;
}

.navbar .dropdown-menu {
    z-index: 99999 !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

/* Card hover effects */
.card {
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    border: none;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-bottom: none;
    font-weight: 600;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

/* Enhanced Form styles */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.required:after {
    content: " *";
    color: var(--primary-color);
}

/* Enhanced Tables */
.table {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(0, 0, 0, 0.02);
}

/* Button styles */
.btn-icon {
    display: inline-flex;
    align-items: center;
}

.btn-icon i {
    margin-right: 0.5rem;
}

/* Status badges */
.badge-draft {
    background-color: #6c757d;
}

.badge-sent {
    background-color: #17a2b8;
}

.badge-accepted {
    background-color: #28a745;
}

.badge-rejected {
    background-color: #dc3545;
}

.badge-paid {
    background-color: #28a745;
}

.badge-overdue {
    background-color: #dc3545;
}

.badge-delivered {
    background-color: #28a745;
}

.badge-returned {
    background-color: #ffc107;
}

/* Logo styles for company */
.logo {
    color: #dc3545;
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
    display: flex;
    align-items: center;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background-color: #dc3545;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.logo-icon span {
    color: white;
    font-size: 24px;
}

/* Permission styles */
.permission-table th,
.permission-table td {
    text-align: center;
}

.permission-yes {
    color: #28a745;
}

.permission-no {
    color: #dc3545;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }

    .card {
        border: none !important;
    }

    .card-header,
    .card-footer {
        background-color: transparent !important;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    thead {
        background-color: #f2f2f2;
    }
}

/* Enhanced Footer */
.footer {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(220, 53, 69, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Alerts */
.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-left: 4px solid #28a745;
    border-radius: var(--border-radius);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-left: 4px solid #dc3545;
    border-radius: var(--border-radius);
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left: 4px solid #ffc107;
    border-radius: var(--border-radius);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-left: 4px solid #17a2b8;
    border-radius: var(--border-radius);
}

/* Unified Delete Modal Style - Like in the image */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* Delete Modal Header - Red like in image */
.modal-header.bg-danger, .delete-modal .modal-header {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 20px 25px;
}

.modal-header.bg-danger .modal-title, .delete-modal .modal-title {
    font-weight: 600;
    font-size: 18px;
    color: white !important;
}

.modal-header.bg-danger .btn-close, .delete-modal .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-header.bg-danger .btn-close:hover, .delete-modal .btn-close:hover {
    opacity: 1;
}

/* Delete Modal Body */
.delete-modal .modal-body {
    padding: 25px;
    background: white;
}

.delete-modal .modal-body p {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 1.5;
}

.delete-modal .modal-body .text-danger {
    color: #dc3545 !important;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Force Modal Footer to Show - Critical Fix */
.modal .modal-footer,
.delete-modal .modal-footer,
div[id*="deleteModal"] .modal-footer {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0.75rem !important;
    border-top: 1px solid #dee2e6 !important;
    border-bottom-right-radius: calc(0.3rem - 1px) !important;
    border-bottom-left-radius: calc(0.3rem - 1px) !important;
    background: #f8f9fa !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 50px !important;
    position: relative !important;
    z-index: 1 !important;
}

.modal-footer form,
.delete-modal .modal-footer form {
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Force Modal Buttons to Show */
.modal-footer .btn,
.delete-modal .modal-footer .btn,
div[id*="deleteModal"] .modal-footer .btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 8px 16px !important;
    margin: 0 5px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 2 !important;
    min-width: 80px !important;
    text-align: center !important;
}

.modal-footer .btn-secondary,
.delete-modal .modal-footer .btn-secondary {
    background-color: #6c757d !important;
    border: 1px solid #6c757d !important;
    color: #ffffff !important;
}

.modal-footer .btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #5a6268 !important;
    color: #ffffff !important;
}

.delete-modal .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.modal-footer .btn-delete-confirm,
.delete-modal .modal-footer .btn-delete-confirm,
div[id*="deleteModal"] .modal-footer .btn-delete-confirm {
    background-color: #dc3545 !important;
    border: 1px solid #dc3545 !important;
    color: #ffffff !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.modal-footer .btn-delete-confirm:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
    color: #ffffff !important;
}

.delete-modal .btn-danger:hover, .delete-modal .btn-delete-confirm:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Ensure all delete modals work properly */
.modal.delete-modal {
    z-index: 1055 !important;
}

.modal.delete-modal .modal-backdrop {
    z-index: 1050 !important;
}

/* Fix any modal conflicts */
.modal.show {
    display: block !important;
}

.modal-backdrop.show {
    opacity: 0.6 !important;
}

/* Simple Modal Fix */
.modal-dialog-centered {
    display: flex !important;
    align-items: center !important;
    min-height: calc(100% - 1rem) !important;
}

/* Ensure delete buttons are clickable */
.btn-delete, .btn-outline-danger, .btn-delete-modern,
.btn-delete-glow, .btn-delete-neon, .btn-delete-3d, .btn-delete-text {
    cursor: pointer !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .navbar-nav {
        text-align: center;
    }

    .card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}
