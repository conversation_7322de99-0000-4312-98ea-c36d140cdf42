from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from app import db
from app.models import User
from app.forms.password_reset import RequestPasswordResetForm, ResetPasswordForm, ChangePasswordForm
import secrets
from datetime import datetime, timedelta

password_bp = Blueprint('password', __name__)

@password_bp.route('/request_password_reset', methods=['GET', 'POST'])
def request_password_reset():
    """Request password reset"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = RequestPasswordResetForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user:
            # Generate reset token
            token = secrets.token_urlsafe(32)
            user.reset_token = token
            user.reset_token_expiry = datetime.utcnow() + timedelta(hours=1)
            db.session.commit()
            
            # In a real application, you would send an email here
            # For now, we'll just show a success message
            flash(f'Un lien de réinitialisation a été envoyé à {form.email.data}. '
                  f'Le lien expire dans 1 heure.', 'success')
            
            # For development, show the reset link
            reset_url = url_for('password.reset_password', token=token, _external=True)
            flash(f'Lien de développement: {reset_url}', 'info')
            
        else:
            flash('Si cette adresse email existe dans notre système, '
                  'vous recevrez un lien de réinitialisation.', 'info')
        
        return redirect(url_for('auth.login'))
    
    return render_template('auth/request_password_reset.html', form=form)

@password_bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Reset password with token"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    # Find user with valid token
    user = User.query.filter_by(reset_token=token).first()
    if not user or not user.reset_token_expiry or user.reset_token_expiry < datetime.utcnow():
        flash('Le lien de réinitialisation est invalide ou a expiré.', 'danger')
        return redirect(url_for('password.request_password_reset'))
    
    form = ResetPasswordForm()
    if form.validate_on_submit():
        # Update password
        user.password_hash = generate_password_hash(form.password.data)
        user.reset_token = None
        user.reset_token_expiry = None
        db.session.commit()
        
        flash('Votre mot de passe a été mis à jour avec succès. '
              'Vous pouvez maintenant vous connecter.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password.html', form=form)

@password_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password for logged in user"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        # Verify current password
        if not check_password_hash(current_user.password_hash, form.current_password.data):
            flash('Le mot de passe actuel est incorrect.', 'danger')
            return render_template('auth/change_password.html', form=form)
        
        # Update password
        current_user.password_hash = generate_password_hash(form.new_password.data)
        db.session.commit()
        
        flash('Votre mot de passe a été changé avec succès.', 'success')
        return redirect(url_for('main.index'))
    
    return render_template('auth/change_password.html', form=form)
