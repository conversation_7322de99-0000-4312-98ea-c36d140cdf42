/**
 * RELIABLE DELETE SYSTEM
 * Simple and guaranteed to work delete functionality
 */

// Global delete function - works everywhere
function deleteItem(itemName, deleteUrl, itemType = 'élément') {
    // Simple confirmation dialog
    const confirmMessage = `⚠️ ATTENTION ⚠️\n\nVoulez-vous vraiment supprimer ${itemType} :\n"${itemName}"\n\n❌ Cette action est IRRÉVERSIBLE ❌\n\nCliquez sur OK pour confirmer la suppression.`;
    
    if (confirm(confirmMessage)) {
        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.id = 'deleteLoading';
        loadingMessage.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #dc3545;
            color: white;
            padding: 20px 40px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 99999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            text-align: center;
        `;
        loadingMessage.innerHTML = `
            <div style="font-size: 18px;">🗑️ Suppression en cours...</div>
            <div style="font-size: 14px; margin-top: 10px;">Veuillez patienter...</div>
        `;
        document.body.appendChild(loadingMessage);
        
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        form.style.display = 'none';
        
        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        
        // Submit form after short delay
        setTimeout(() => {
            form.submit();
        }, 500);
        
        return true;
    }
    return false;
}

// Initialize delete buttons when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🗑️ Initializing reliable delete system...');
    
    // Find and fix all delete buttons
    initializeDeleteButtons();
    
    // Watch for new buttons added dynamically
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                initializeDeleteButtons();
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

function initializeDeleteButtons() {
    // Find all potential delete buttons
    const deleteButtons = document.querySelectorAll(`
        .btn-delete,
        .btn-outline-danger,
        .btn-danger,
        [onclick*="delete"],
        [onclick*="Delete"],
        [onclick*="supprimer"],
        [onclick*="Supprimer"],
        [data-bs-target*="deleteModal"],
        [data-bs-target*="DeleteModal"],
        button[title*="Supprimer"],
        button[title*="supprimer"],
        a[title*="Supprimer"],
        a[title*="supprimer"]
    `);
    
    console.log(`Found ${deleteButtons.length} delete buttons to fix`);
    
    deleteButtons.forEach(button => {
        // Skip if already processed
        if (button.hasAttribute('data-delete-processed')) {
            return;
        }
        
        // Mark as processed
        button.setAttribute('data-delete-processed', 'true');
        
        // Remove any existing event listeners by cloning
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Add new reliable click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Extract item information
            const itemName = extractItemName(this);
            const deleteUrl = extractDeleteUrl(this);
            const itemType = extractItemType(this);
            
            console.log('Delete button clicked:', { itemName, deleteUrl, itemType });
            
            if (!deleteUrl) {
                alert('❌ Erreur: URL de suppression non trouvée.\n\nVeuillez contacter l\'administrateur.');
                return;
            }
            
            // Call delete function
            deleteItem(itemName, deleteUrl, itemType);
        });
    });
}

function extractItemName(button) {
    // Try multiple methods to get item name
    let itemName = button.getAttribute('data-item-name');
    
    if (!itemName) {
        // Try to find name in the same row
        const row = button.closest('tr');
        if (row) {
            const cells = row.querySelectorAll('td');
            if (cells.length > 1) {
                itemName = cells[1].textContent.trim();
            }
        }
    }
    
    if (!itemName) {
        // Try to find name in nearby elements
        const card = button.closest('.card');
        if (card) {
            const title = card.querySelector('.card-title, h1, h2, h3, h4, h5, h6');
            if (title) {
                itemName = title.textContent.trim();
            }
        }
    }
    
    return itemName || 'cet élément';
}

function extractDeleteUrl(button) {
    // Try multiple methods to get delete URL
    let deleteUrl = button.getAttribute('data-delete-url');
    
    if (!deleteUrl) {
        // Try onclick attribute
        const onclick = button.getAttribute('onclick');
        if (onclick) {
            const urlMatch = onclick.match(/['"]([^'"]*delete[^'"]*)['"]/i);
            if (urlMatch) {
                deleteUrl = urlMatch[1];
            }
        }
    }
    
    if (!deleteUrl) {
        // Try form action
        const form = button.closest('form');
        if (form) {
            deleteUrl = form.action;
        }
    }
    
    if (!deleteUrl) {
        // Try href for links
        if (button.tagName === 'A') {
            deleteUrl = button.href;
        }
    }
    
    return deleteUrl;
}

function extractItemType(button) {
    let itemType = button.getAttribute('data-item-type');
    
    if (!itemType) {
        // Try to determine from URL or context
        const deleteUrl = extractDeleteUrl(button);
        if (deleteUrl) {
            if (deleteUrl.includes('client')) itemType = 'le client';
            else if (deleteUrl.includes('supplier')) itemType = 'le fournisseur';
            else if (deleteUrl.includes('quote')) itemType = 'le devis';
            else if (deleteUrl.includes('invoice')) itemType = 'la facture';
            else if (deleteUrl.includes('delivery')) itemType = 'le bon de livraison';
            else if (deleteUrl.includes('product')) itemType = 'le produit';
            else if (deleteUrl.includes('equipment')) itemType = 'l\'équipement';
            else if (deleteUrl.includes('user')) itemType = 'l\'utilisateur';
        }
    }
    
    return itemType || 'cet élément';
}

// Make function globally available
window.deleteItem = deleteItem;
window.confirmDelete = deleteItem; // Backward compatibility

console.log('✅ Reliable delete system loaded successfully');
