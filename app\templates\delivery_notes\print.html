<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon <PERSON>raison N° {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }

        .company-name {
            font-size: 24pt;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }

        .document-title {
            font-size: 20pt;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 10pt;
            margin: 10px 0;
        }

        .status-delivered {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-box {
            width: 48%;
        }

        .info-title {
            font-weight: bold;
            color: #007bff;
            border-bottom: 1px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .info-line {
            margin-bottom: 5px;
        }

        .table-container {
            margin: 30px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .delivery-instructions {
            background: #e7f3ff;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
            font-size: 10pt;
        }

        .signatures {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            width: 30%;
            text-align: center;
            border: 1px solid #ddd;
            padding: 20px;
            min-height: 80px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #007bff;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            margin: 20px 5px 5px;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10pt;
            color: #666;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-print {
            background: #007bff;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="goToDeliveryNotes()">Fermer</button>
    </div>

    <!-- En-tête -->
    <div class="header">
        <div class="company-name">
            {% if company and company.name %}
                {{ company.name }}
            {% else %}
                GESTION D'EXTINCTEURS
            {% endif %}
        </div>
        <div style="font-size: 12pt; color: #666;">
            {% if company and company.address %}
                {{ company.address }}<br>
            {% endif %}
            {% if company and company.phone %}
                Tél: {{ company.phone }}
            {% endif %}
            {% if company and company.email %}
                | Email: {{ company.email }}
            {% endif %}
        </div>
        <div class="document-title">BON DE LIVRAISON</div>

        <!-- Statut de livraison -->
        <div class="status-badge
            {% if delivery_note and delivery_note.status == 'delivered' %}status-delivered
            {% elif delivery_note and delivery_note.status == 'cancelled' %}status-cancelled
            {% else %}status-pending{% endif %}">
            {% if delivery_note and delivery_note.status == 'delivered' %}✓ LIVRÉ
            {% elif delivery_note and delivery_note.status == 'cancelled' %}❌ ANNULÉ
            {% else %}⏳ EN ATTENTE{% endif %}
        </div>
    </div>

    <!-- Informations -->
    <div class="info-section">
        <div class="info-box">
            <div class="info-title">LIVRÉ À:</div>
            {% if delivery_note and delivery_note.client %}
                <div class="info-line"><strong>{{ delivery_note.client.name }}</strong></div>
                {% if delivery_note.client.address %}
                    <div class="info-line">{{ delivery_note.client.address }}</div>
                {% endif %}
                {% if delivery_note.client.city %}
                    <div class="info-line">{{ delivery_note.client.city }}</div>
                {% endif %}
                {% if delivery_note.client.phone %}
                    <div class="info-line">Tél: {{ delivery_note.client.phone }}</div>
                {% endif %}
                {% if delivery_note.client.email %}
                    <div class="info-line">Email: {{ delivery_note.client.email }}</div>
                {% endif %}
            {% else %}
                <div class="info-line"><strong>Client Demo</strong></div>
                <div class="info-line">123 Rue Example</div>
                <div class="info-line">Casablanca, Maroc</div>
                <div class="info-line">Tél: +212 5 22 XX XX XX</div>
            {% endif %}
        </div>

        <div class="info-box">
            <div class="info-title">DÉTAILS DE LIVRAISON:</div>
            <div class="info-line">
                <strong>N° Bon:</strong>
                {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}
            </div>
            <div class="info-line">
                <strong>Date livraison:</strong>
                {{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note and delivery_note.date else '01/01/2024' }}
            </div>
            <div class="info-line">
                <strong>Heure:</strong>
                {{ delivery_note.date.strftime('%H:%M') if delivery_note and delivery_note.date else '10:00' }}
            </div>
            {% if delivery_note and delivery_note.invoice %}
            <div class="info-line">
                <strong>Facture N°:</strong> {{ delivery_note.invoice.invoice_number }}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Instructions de livraison -->
    <div class="delivery-instructions">
        <strong>Instructions de livraison:</strong>
        Veuillez vérifier tous les produits avant de signer le bon de réception.
        En cas de dommage ou de manquant, veuillez le noter dans la section observations.
    </div>

    <!-- Tableau des produits -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 40%;">Désignation</th>
                    <th style="width: 10%;">Qté Commandée</th>
                    <th style="width: 10%;">Qté Livrée</th>
                    <th style="width: 15%;">État</th>
                    <th style="width: 20%;">Observations</th>
                </tr>
            </thead>
            <tbody>
                {% if delivery_note and delivery_note.items %}
                    {% for item in delivery_note.items %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            <strong>{{ item.product.name if item.product else 'Produit supprimé' }}</strong>
                            {% if item.product and item.product.reference %}
                                <br><small>Réf: {{ item.product.reference }}</small>
                            {% endif %}
                        </td>
                        <td class="text-center">{{ item.quantity }}</td>
                        <td class="text-center">_______</td>
                        <td class="text-center">☐ OK ☐ Défaut</td>
                        <td>_________________</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <!-- Données de démonstration -->
                    <tr>
                        <td>1</td>
                        <td><strong>Extincteur CO2 5kg</strong><br><small>Réf: EXT-CO2-5</small></td>
                        <td class="text-center">2</td>
                        <td class="text-center">_______</td>
                        <td class="text-center">☐ OK ☐ Défaut</td>
                        <td>_________________</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td><strong>Extincteur Poudre 6kg</strong><br><small>Réf: EXT-POU-6</small></td>
                        <td class="text-center">3</td>
                        <td class="text-center">_______</td>
                        <td class="text-center">☐ OK ☐ Défaut</td>
                        <td>_________________</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td><strong>Support mural</strong><br><small>Réf: SUP-MUR-01</small></td>
                        <td class="text-center">5</td>
                        <td class="text-center">_______</td>
                        <td class="text-center">☐ OK ☐ Défaut</td>
                        <td>_________________</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Observations générales -->
    <div style="margin: 20px 0;">
        <strong>Observations générales:</strong>
        <div style="border: 1px solid #ddd; min-height: 60px; padding: 10px; margin-top: 5px;">
            {% if delivery_note and delivery_note.notes %}
                {{ delivery_note.notes }}
            {% endif %}
        </div>
    </div>

    <!-- Signatures -->
    <div class="signatures">
        <div class="signature-box">
            <div class="signature-title">LIVREUR</div>
            <div class="signature-line"></div>
            <div style="font-size: 9pt; margin-top: 5px;">
                Nom: _______________<br>
                Date: ______________
            </div>
        </div>

        <div class="signature-box">
            <div class="signature-title">RÉCEPTIONNAIRE</div>
            <div class="signature-line"></div>
            <div style="font-size: 9pt; margin-top: 5px;">
                Nom: _______________<br>
                Date: ______________
            </div>
        </div>

        <div class="signature-box">
            <div class="signature-title">RESPONSABLE</div>
            <div class="signature-line"></div>
            <div style="font-size: 9pt; margin-top: 5px;">
                Nom: _______________<br>
                Date: ______________
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        <p><strong>Important:</strong> Conservez ce bon comme preuve de livraison</p>
        <p style="font-size: 9pt;">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% else %}
                Pour toute question: <EMAIL> | Tél: +212 5 37 XX XX XX
            {% endif %}
        </p>
    </div>

    <script>
        function goToDeliveryNotes() {
            try {
                window.location.href = '/commercial/delivery_notes';
            } catch (e) {
                try {
                    window.history.back();
                } catch (e2) {
                    try {
                        window.close();
                    } catch (e3) {
                        window.location.href = '/';
                    }
                }
            }
        }
    </script>
</body>
</html>
