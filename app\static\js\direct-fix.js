/**
 * DIRECT FIX FOR EXISTING PAGES
 * Immediate fix for buttons on current pages
 */

console.log('⚡ Direct Fix System Loading...');

// Immediate execution
(function() {
    'use strict';
    
    console.log('🔧 Starting immediate button fix...');
    
    // Wait for DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyDirectFix);
    } else {
        applyDirectFix();
    }
    
    // Also apply after a short delay
    setTimeout(applyDirectFix, 500);
    setTimeout(applyDirectFix, 1000);
    setTimeout(applyDirectFix, 2000);
    
    function applyDirectFix() {
        console.log('🔧 Applying direct fix...');
        
        // Fix all delete buttons immediately
        fixDeleteButtonsNow();
        
        // Fix all close buttons immediately
        fixCloseButtonsNow();
        
        // Fix modal buttons
        fixModalButtonsNow();
        
        console.log('✅ Direct fix applied!');
    }
    
    function fixDeleteButtonsNow() {
        console.log('🗑️ Fixing delete buttons now...');
        
        // Find ALL possible delete buttons
        const selectors = [
            'button.btn-danger',
            'a.btn-danger',
            'button.btn-outline-danger',
            'a.btn-outline-danger',
            'button[onclick*="delete"]',
            'a[onclick*="delete"]',
            'button[onclick*="supprimer"]',
            'a[onclick*="supprimer"]',
            'button[class*="delete"]',
            'a[class*="delete"]',
            '.fa-trash',
            '.fa-trash-alt'
        ];
        
        selectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(element => {
                    // Get the actual button
                    let button = element;
                    if (element.classList.contains('fa-trash') || element.classList.contains('fa-trash-alt')) {
                        button = element.closest('button, a');
                    }
                    
                    if (button && !button.hasAttribute('data-direct-fixed')) {
                        button.setAttribute('data-direct-fixed', 'true');
                        
                        console.log('Fixing delete button:', button);
                        
                        // Remove old handlers
                        button.removeAttribute('onclick');
                        
                        // Clone to remove all event listeners
                        const newButton = button.cloneNode(true);
                        button.parentNode.replaceChild(newButton, button);
                        
                        // Add new handler
                        newButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            e.stopImmediatePropagation();
                            
                            console.log('Delete button clicked:', this);
                            handleDeleteClick(this);
                        });
                    }
                });
            } catch (error) {
                console.error('Error fixing selector:', selector, error);
            }
        });
    }
    
    function fixCloseButtonsNow() {
        console.log('🚪 Fixing close buttons now...');
        
        const selectors = [
            'button.btn-secondary',
            'a.btn-secondary',
            'button[onclick*="close"]',
            'a[onclick*="close"]',
            'button[onclick*="fermer"]',
            'a[onclick*="fermer"]',
            'button[onclick*="goBack"]',
            'a[onclick*="goBack"]'
        ];
        
        selectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(button => {
                    // Skip modal dismiss buttons
                    if (button.hasAttribute('data-bs-dismiss')) return;
                    
                    if (!button.hasAttribute('data-direct-fixed')) {
                        button.setAttribute('data-direct-fixed', 'true');
                        
                        console.log('Fixing close button:', button);
                        
                        // Remove old handlers
                        button.removeAttribute('onclick');
                        
                        // Clone to remove all event listeners
                        const newButton = button.cloneNode(true);
                        button.parentNode.replaceChild(newButton, button);
                        
                        // Add new handler
                        newButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            e.stopImmediatePropagation();
                            
                            console.log('Close button clicked:', this);
                            handleCloseClick();
                        });
                    }
                });
            } catch (error) {
                console.error('Error fixing selector:', selector, error);
            }
        });
    }
    
    function fixModalButtonsNow() {
        console.log('📋 Fixing modal buttons now...');
        
        // Fix confirm buttons in modals
        document.querySelectorAll('.modal .btn-danger, .modal .btn-confirm').forEach(button => {
            if (!button.hasAttribute('data-modal-fixed')) {
                button.setAttribute('data-modal-fixed', 'true');
                
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const modal = this.closest('.modal');
                    const form = modal ? modal.querySelector('form') : null;
                    
                    if (form) {
                        console.log('Submitting modal form:', form);
                        form.submit();
                    }
                });
            }
        });
    }
    
    function handleDeleteClick(button) {
        console.log('🗑️ Handling delete click:', button);
        
        try {
            // Get item info
            const itemName = getItemInfo(button);
            const deleteUrl = getDeleteURL(button);
            const itemType = getItemTypeFromURL(deleteUrl);
            
            console.log('Delete info:', { itemName, deleteUrl, itemType });
            
            if (!deleteUrl) {
                alert('❌ Erreur: URL de suppression non trouvée.');
                return;
            }
            
            // Show confirmation
            const message = `⚠️ SUPPRESSION ⚠️\n\nVoulez-vous vraiment supprimer ${itemType} :\n"${itemName}"\n\n❌ Cette action est IRRÉVERSIBLE ❌\n\nCliquez sur OK pour confirmer.`;
            
            if (confirm(message)) {
                console.log('✅ Delete confirmed');
                
                // Show loading
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Suppression...';
                
                // Submit delete
                submitDelete(deleteUrl);
            }
            
        } catch (error) {
            console.error('Delete error:', error);
            alert('❌ Erreur lors de la suppression: ' + error.message);
        }
    }
    
    function handleCloseClick() {
        console.log('🚪 Handling close click');
        
        try {
            // Try different close methods
            if (window.opener && !window.opener.closed) {
                window.close();
            } else if (history.length > 1) {
                history.back();
            } else {
                // Smart redirect
                const path = window.location.pathname;
                let target = '/';
                
                if (path.includes('invoice')) target = '/invoices/';
                else if (path.includes('quote')) target = '/quotes/';
                else if (path.includes('delivery')) target = '/delivery-notes/';
                else if (path.includes('client')) target = '/clients/';
                else if (path.includes('supplier')) target = '/suppliers/';
                else if (path.includes('product')) target = '/products/';
                
                window.location.href = target;
            }
        } catch (error) {
            console.error('Close error:', error);
            window.location.href = '/';
        }
    }
    
    function getItemInfo(button) {
        // Try multiple methods
        let name = button.getAttribute('data-item-name');
        
        if (!name) {
            const row = button.closest('tr');
            if (row) {
                const cells = row.querySelectorAll('td');
                if (cells.length > 1) {
                    name = cells[1].textContent.trim();
                }
            }
        }
        
        if (!name) {
            const card = button.closest('.card');
            if (card) {
                const title = card.querySelector('.card-title, h1, h2, h3');
                if (title) {
                    name = title.textContent.trim();
                }
            }
        }
        
        return name || 'cet élément';
    }
    
    function getDeleteURL(button) {
        let url = button.getAttribute('data-delete-url');
        
        if (!url && button.tagName === 'A') {
            url = button.href;
        }
        
        if (!url) {
            const form = button.closest('form');
            if (form) {
                url = form.action;
            }
        }
        
        if (!url) {
            const onclick = button.getAttribute('onclick');
            if (onclick) {
                const match = onclick.match(/['"]([^'"]*)['"]/);
                if (match) {
                    url = match[1];
                }
            }
        }
        
        return url;
    }
    
    function getItemTypeFromURL(url) {
        if (!url) return 'cet élément';
        
        if (url.includes('client')) return 'le client';
        if (url.includes('supplier')) return 'le fournisseur';
        if (url.includes('quote')) return 'le devis';
        if (url.includes('invoice')) return 'la facture';
        if (url.includes('delivery')) return 'le bon de livraison';
        if (url.includes('product')) return 'le produit';
        
        return 'cet élément';
    }
    
    function submitDelete(url) {
        console.log('📤 Submitting delete to:', url);
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = url;
        form.style.display = 'none';
        
        // Add CSRF token
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'csrf_token';
            input.value = csrfMeta.getAttribute('content');
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
    
})();

console.log('✅ Direct Fix System loaded!');
