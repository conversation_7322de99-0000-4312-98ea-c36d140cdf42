<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal de Suppression</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="app/static/css/reliable-buttons.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="fas fa-test-tube me-2"></i>
            Test du Modal de Suppression
        </h1>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Test des Boutons de Suppression</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Facture FAC-001</td>
                                    <td>Facture</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary me-1">
                                            <i class="fas fa-eye"></i> Voir
                                        </button>
                                        <button class="btn btn-sm btn-warning me-1">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="delete" data-delete-url="/test/delete/1">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Devis DEV-002</td>
                                    <td>Devis</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary me-1">
                                            <i class="fas fa-eye"></i> Voir
                                        </button>
                                        <button class="btn btn-sm btn-warning me-1">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="delete" data-delete-url="/test/delete/2">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Bon de Livraison BL-003</td>
                                    <td>Bon de Livraison</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary me-1">
                                            <i class="fas fa-eye"></i> Voir
                                        </button>
                                        <button class="btn btn-sm btn-warning me-1">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="delete" data-delete-url="/test/delete/3">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Instructions de Test:</h6>
                            <ul class="mb-0">
                                <li>✅ Les boutons "Voir" et "Modifier" doivent fonctionner normalement</li>
                                <li>✅ Les boutons "Supprimer" doivent afficher un modal élégant</li>
                                <li>✅ Le modal doit avoir des boutons "Annuler" et "Supprimer" fonctionnels</li>
                                <li>✅ Pas de fond noir sans boutons</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Résultats du Test</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p>Cliquez sur les boutons ci-dessus pour tester le fonctionnement.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Smart Buttons JS -->
    <script src="app/static/js/smart-buttons.js"></script>

    <!-- Test Script -->
    <script>
        // Monitor button clicks for testing
        document.addEventListener('click', function(e) {
            const resultsDiv = document.getElementById('testResults');
            
            if (e.target.closest('.btn-primary')) {
                resultsDiv.innerHTML += '<div class="alert alert-success">✅ Bouton "Voir" cliqué - Fonctionne correctement!</div>';
            }
            
            if (e.target.closest('.btn-warning')) {
                resultsDiv.innerHTML += '<div class="alert alert-warning">✅ Bouton "Modifier" cliqué - Fonctionne correctement!</div>';
            }
            
            if (e.target.closest('.btn-danger')) {
                resultsDiv.innerHTML += '<div class="alert alert-info">🔄 Bouton "Supprimer" cliqué - Modal devrait apparaître...</div>';
            }
        });

        // Override submitDelete for testing
        window.submitDelete = function(url) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML += '<div class="alert alert-success">✅ Suppression confirmée pour: ' + url + '</div>';
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('smartDeleteModal'));
            if (modal) {
                modal.hide();
            }
        };

        console.log('🧪 Test page loaded successfully!');
    </script>
</body>
</html>
