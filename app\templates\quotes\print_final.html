<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else '143' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 0;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Header rouge avec logo et titre */
        .header {
            background-color: #dc3545;
            color: white;
            height: 80px;
            display: flex;
            align-items: center;
            padding: 0 30px;
            margin-bottom: 0;
        }

        .logo-container {
            width: 200px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            margin-right: 30px;
            overflow: hidden;
        }

        .logo-container img {
            max-width: 200px;
            max-height: 80px;
            object-fit: contain;
        }

        .document-title {
            font-size: 28pt;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0;
        }

        /* Section Client avec fond gris */
        .client-section {
            background-color: #f8f9fa;
            padding: 15px 15mm;
            margin-bottom: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
        }

        .client-info {
            flex: 1;
        }

        .document-info {
            flex: 1;
            text-align: left;
            margin-left: 50px;
        }

        .info-line {
            margin-bottom: 5px;
            font-size: 11pt;
        }

        .info-label {
            color: #666;
            display: inline-block;
            min-width: 120px;
        }

        /* Section Objet */
        .object-section {
            padding: 15px 15mm;
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 12pt;
        }

        .object-content {
            font-size: 11pt;
            color: #333;
            min-height: 20px;
            padding: 5px 0;
            border-bottom: 1px solid #dc3545;
        }

        /* Tableau des articles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 15mm;
            width: calc(100% - 30mm);
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 30px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background-color: white;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section des totaux */
        .totals-section {
            margin: 30px 15mm;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 200px;
        }

        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .totals-table .label-col {
            text-align: right;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .totals-table .value-col {
            text-align: right;
            min-width: 100px;
        }

        .total-ttc {
            background-color: #dc3545 !important;
            color: white !important;
            font-weight: bold !important;
        }

        /* Service info */
        .service-info {
            margin: 30px;
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }

        /* Footer */
        .footer {
            margin-top: 50px;
            padding: 20px 15mm;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1px solid #ddd;
            background-color: #f8f9fa;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4 portrait;
                margin: 15mm 10mm 20mm 10mm;

                @top-left {
                    content: element(header-content);
                }

                @bottom-center {
                    content: element(footer-content);
                }
            }

            html, body {
                margin: 0 !important;
                padding: 0 !important;
                font-size: 11pt !important;
                line-height: 1.4 !important;
                background: white !important;
            }

            .page-container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                width: 100% !important;
                min-height: auto !important;
            }

            /* Header for all pages */
            .print-header {
                position: running(header-content);
                background-color: #dc3545 !important;
                color: white !important;
                padding: 8px 15px !important;
                margin-bottom: 10px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: space-between !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-header .company-info {
                font-size: 10pt !important;
                font-weight: bold !important;
            }

            .print-header .document-info {
                font-size: 10pt !important;
                text-align: right !important;
            }

            /* Footer for all pages */
            .print-footer {
                position: running(footer-content);
                text-align: center !important;
                font-size: 8pt !important;
                color: #666 !important;
                padding: 5px 0 !important;
                border-top: 1px solid #ddd !important;
            }

            /* Main header - only on first page */
            .header {
                background-color: #dc3545 !important;
                color: white !important;
                height: 70px !important;
                padding: 0 20px !important;
                margin-bottom: 15px !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .logo-container {
                width: 150px !important;
                height: 60px !important;
                margin-right: 20px !important;
            }

            .document-title {
                font-size: 28pt !important;
            }

            /* Client section */
            .client-section {
                background-color: #f8f9fa !important;
                padding: 12px 20px !important;
                margin-bottom: 15px !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Object section */
            .object-section {
                padding: 10px 20px !important;
                margin-bottom: 15px !important;
            }

            /* Table styling for print */
            .items-table {
                width: 100% !important;
                margin: 0 !important;
                font-size: 10pt !important;
                border-collapse: collapse !important;
                page-break-inside: auto !important;
            }

            .items-table thead {
                display: table-header-group !important;
            }

            .items-table tbody {
                display: table-row-group !important;
            }

            .items-table th {
                background-color: #dc3545 !important;
                color: white !important;
                padding: 8px 6px !important;
                font-size: 9pt !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .items-table td {
                padding: 6px !important;
                font-size: 9pt !important;
                border: 1px solid #ddd !important;
                page-break-inside: avoid !important;
            }

            .items-table tr {
                page-break-inside: avoid !important;
            }

            /* Totals section */
            .totals-section {
                margin: 20px 0 !important;
                page-break-inside: avoid !important;
            }

            .totals-table {
                font-size: 10pt !important;
            }

            .total-ttc {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Footer */
            .footer {
                margin-top: 20px !important;
                padding: 10px 0 !important;
                font-size: 8pt !important;
                text-align: center !important;
                color: #666 !important;
                border-top: 1px solid #ddd !important;
                page-break-inside: avoid !important;
            }

            /* Page breaks */
            .page-break {
                page-break-before: always !important;
            }

            .no-page-break {
                page-break-inside: avoid !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="goToQuotes()">Fermer</button>
    </div>

    <!-- Print Header - appears on every page -->
    <div class="print-header" style="display: none;">
        <div class="company-info">
            {% if company and company.name %}{{ company.name }}{% else %}Nom de l'entreprise{% endif %}
        </div>
        <div class="document-info">
            Devis N° {{ quote.quote_number if quote else '143' }} - Page
        </div>
    </div>

    <!-- Print Footer - appears on every page -->
    <div class="print-footer" style="display: none;">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% elif company %}
            {% if company.name %}{{ company.name }}{% endif %}
            {% if company.address %} - {{ company.address }}{% endif %}
            {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
            {% if company.email %} - Email: {{ company.email }}{% endif %}
            {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
        {% endif %}
    </div>

    <div class="page-container">
        <!-- Header rouge avec logo et titre - First page only -->
        <div class="header">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                {% else %}
                    Logo
                {% endif %}
            </div>
            <div class="document-title">Devis</div>
        </div>

    <!-- Section Client avec fond gris -->
    <div class="client-section">
        <div class="client-title">Client :</div>
        <div class="client-details">
            <div class="client-info">
                <div class="info-line">
                    <span class="info-label">Nom du client :</span>
                    {{ quote.client.name if quote and quote.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Ice :</span>
                    {{ quote.client.ice if quote and quote.client and quote.client.ice else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone :</span>
                    {{ quote.client.phone if quote and quote.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Email :</span>
                    {{ quote.client.email if quote and quote.client else '' }}
                </div>
            </div>
            <div class="document-info">
                <div class="info-line">
                    <span class="info-label">Date du devis :</span>
                    {{ quote.date.strftime('%d.%m.%Y') if quote and quote.date else '1.6.2021' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Référence du devis :</span>
                    {{ quote.quote_number if quote else '143' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Date de validité :</span>
                    {{ quote.expiration_date.strftime('%d.%m.%Y') if quote and quote.expiration_date else '15.6.2021' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Objet -->
    <div class="object-section">
        <div class="object-title">Objet :</div>
        <div class="object-content">{{ quote.notes if quote and quote.notes else '' }}</div>
    </div>

    <!-- Tableau des articles -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 40%;">Description</th>
                <th style="width: 15%;">Unité</th>
                <th style="width: 15%;">Quantité</th>
                <th style="width: 15%;">Prix U.HT</th>
                <th style="width: 15%;">Prix T.HT</th>
            </tr>
        </thead>
        <tbody>
            {% if quote and quote.items %}
                {% for item in quote.items %}
                <tr class="no-page-break">
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>Unité</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ "%.2f"|format(item.unit_price) }} DH</td>
                    <td>{{ "%.2f"|format(item.total) }} DH</td>
                </tr>
                {% endfor %}
                <!-- Add empty rows only if needed for formatting -->
                {% set items_count = quote.items|list|length if quote and quote.items else 0 %}
                {% if items_count < 5 %}
                    {% for i in range(5 - items_count) %}
                    <tr>
                        <td class="description-col">&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endfor %}
                {% endif %}
            {% else %}
                <!-- Lignes vides par défaut -->
                {% for i in range(5) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section des totaux -->
    <div class="totals-section no-page-break">
        <table class="totals-table">
            <tr>
                <td class="label-col">Total HT</td>
                <td class="value-col">{{ "%.2f"|format(quote.subtotal) if quote else '0,00' }} DH</td>
            </tr>
            <tr>
                <td class="label-col">Total TVA (20%)</td>
                <td class="value-col">{{ "%.2f"|format(quote.tax_amount) if quote and quote.tax_amount else '0,00' }} DH</td>
            </tr>
            <tr>
                <td class="label-col total-ttc">Total TTC</td>
                <td class="value-col total-ttc">{{ "%.2f"|format(quote.total) if quote else '0,00' }} DH</td>
            </tr>
        </table>
    </div>



        <!-- Footer -->
        <div class="footer">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% elif company %}
                {% if company.name %}{{ company.name }}{% endif %}
                {% if company.address %} - {{ company.address }}{% endif %}
                {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
                {% if company.email %} - Email: {{ company.email }}{% endif %}
                {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
            {% endif %}
        </div>
    </div>

    <script>
        function goToQuotes() {
            try {
                window.location.href = '/commercial/quotes';
            } catch (e) {
                try {
                    window.history.back();
                } catch (e2) {
                    try {
                        window.close();
                    } catch (e3) {
                        window.location.href = '/';
                    }
                }
            }
        }
    </script>
</body>
</html>
