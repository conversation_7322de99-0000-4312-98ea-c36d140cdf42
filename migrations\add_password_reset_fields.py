"""
Migration script to add password reset fields to User model
Run this script to update the database schema
"""

import sqlite3
import os
from datetime import datetime

def add_password_reset_fields():
    """Add reset_token and reset_token_expiry fields to users table"""
    
    # Database path
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        print("Please make sure the application has been run at least once to create the database.")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Adding password reset fields to users table...")
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'reset_token' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN reset_token VARCHAR(100)")
            print("✅ Added reset_token column")
        else:
            print("ℹ️  reset_token column already exists")
        
        if 'reset_token_expiry' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN reset_token_expiry DATETIME")
            print("✅ Added reset_token_expiry column")
        else:
            print("ℹ️  reset_token_expiry column already exists")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("🎉 Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting password reset fields migration...")
    print("=" * 50)
    
    success = add_password_reset_fields()
    
    if success:
        print("=" * 50)
        print("✅ Migration completed successfully!")
        print("🔐 Password reset functionality is now available.")
        print("\nNew features:")
        print("  • Request password reset via email")
        print("  • Reset password with secure token")
        print("  • Change password when logged in")
        print("  • Token expiration (1 hour)")
    else:
        print("=" * 50)
        print("❌ Migration failed!")
        print("Please check the error messages above and try again.")
    
    print("=" * 50)
