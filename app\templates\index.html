{% extends 'base.html' %}

{% block title %}Tableau de bord - FireGuard Pro{% endblock %}

{% block content %}
<!-- Enhanced Dashboard Header -->
<div class="dashboard-header animate__animated animate__fadeInDown">
    <div class="row align-items-center mb-4">
        <div class="col-lg-8">
            <div class="header-content">
                <h1 class="dashboard-title">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    Tableau de bord
                </h1>
                <p class="dashboard-subtitle">
                    Bienvenue dans FireGuard Pro - Votre solution complète de gestion d'extincteurs et RIA
                </p>
                <div class="dashboard-stats-quick">
                    <span class="quick-stat">
                        <i class="fas fa-clock me-1"></i>
                        Dernière mise à jour: <strong>{{ moment().format('DD/MM/YYYY HH:mm') if moment else 'Maintenant' }}</strong>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-lg-4 text-end">
            <div class="header-actions">
                <button class="btn btn-primary btn-lg modern-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-2"></i>
                    Actualiser
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="row mb-5">
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-primary animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Produits</h6>
                        <h2 class="stats-number">{{ products_count }}</h2>
                        <p class="stats-description">Total des produits</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.products') }}" class="stats-link">
                        <span>Voir tous</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-warning animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Stock faible</h6>
                        <h2 class="stats-number">{{ low_stock_products }}</h2>
                        <p class="stats-description">Produits en rupture</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.stock') }}" class="stats-link">
                        <span>Voir le stock</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-success animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Devis</h6>
                        <h2 class="stats-number">{{ quotes_count }}</h2>
                        <p class="stats-description">Devis créés</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.commercial_quotes') }}" class="stats-link">
                        <span>Voir tous</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-info animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Factures</h6>
                        <h2 class="stats-number">{{ invoices_count }}</h2>
                        <p class="stats-description">Factures émises</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.commercial_invoices') }}" class="stats-link">
                        <span>Voir toutes</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats Row -->
<div class="row mb-5">
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-secondary animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Bons de livraison</h6>
                        <h2 class="stats-number">{{ delivery_notes_count }}</h2>
                        <p class="stats-description">Livraisons effectuées</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.commercial_delivery_notes') }}" class="stats-link">
                        <span>Voir tous</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-primary animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Clients</h6>
                        <h2 class="stats-number">{{ clients_count or 0 }}</h2>
                        <p class="stats-description">Clients actifs</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.clients') }}" class="stats-link">
                        <span>Voir tous</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-success animate__animated animate__fadeInUp" style="animation-delay: 0.7s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Maintenance</h6>
                        <h2 class="stats-number">{{ maintenance_count or 0 }}</h2>
                        <p class="stats-description">Équipements suivis</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="{{ url_for('main.equipment_maintenance') }}" class="stats-link">
                        <span>Voir tous</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card stats-card-warning animate__animated animate__fadeInUp" style="animation-delay: 0.8s">
            <div class="stats-card-body">
                <div class="stats-content">
                    <div class="stats-info">
                        <h6 class="stats-title">Alertes</h6>
                        <h2 class="stats-number">{{ alerts_count or 0 }}</h2>
                        <p class="stats-description">Notifications actives</p>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                </div>
                <div class="stats-footer">
                    <a href="#" class="stats-link">
                        <span>Voir toutes</span>
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Carte Interactive du Maroc -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marked-alt me-2"></i>Carte Interactive du Maroc - Localisation des Clients et Partenaires
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="moroccoMap" style="height: 500px; width: 100%;"></div>
            </div>
            <div class="card-footer">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-primary me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Clients Actifs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-success me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Distributeurs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-warning me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Prospects</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-info me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Partenaires</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
.legend-marker {
    display: inline-block;
    border: 2px solid #fff;
    box-shadow: 0 0 3px rgba(0,0,0,0.3);
}
</style>
{% endblock %}

{% block scripts %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
// Dashboard Enhancement Functions
function refreshDashboard() {
    // Add loading animation
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Actualisation...';
    btn.disabled = true;

    // Simulate refresh (in real app, this would make an AJAX call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Add scroll effect to navbar
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.modern-navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Add counter animation to stats
function animateCounters() {
    const counters = document.querySelectorAll('.stats-number');

    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        const increment = target / 50;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 30);
    });
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Animate counters after a delay
    setTimeout(animateCounters, 500);

    // Add hover effects to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    // Initialize the map centered on Morocco
    var map = L.map('moroccoMap').setView([31.7917, -7.0926], 6);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Define custom icons
    var clientIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #dc3545; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var distributorIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #28a745; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var prospectIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #ffc107; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var partnerIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #17a2b8; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    // Sample data for Morocco cities with clients and partners
    var locations = [
        // Clients Actifs
        {
            name: "Société ABC SARL",
            city: "Casablanca",
            type: "client",
            lat: 33.5731,
            lng: -7.5898,
            info: "Client principal - 15 extincteurs en maintenance"
        },
        {
            name: "Hôtel Royal Mansour",
            city: "Marrakech",
            type: "client",
            lat: 31.6295,
            lng: -7.9811,
            info: "Hôtel de luxe - 45 extincteurs et RIA"
        },
        {
            name: "Usine Textile Nord",
            city: "Tanger",
            type: "client",
            lat: 35.7595,
            lng: -5.8340,
            info: "Industrie textile - 80 extincteurs"
        },
        {
            name: "Centre Commercial Mega Mall",
            city: "Rabat",
            type: "client",
            lat: 34.0209,
            lng: -6.8416,
            info: "Centre commercial - 120 extincteurs"
        },
        {
            name: "Port de Mohammedia",
            city: "Mohammedia",
            type: "client",
            lat: 33.6866,
            lng: -7.3674,
            info: "Infrastructure portuaire - 200 extincteurs"
        },

        // Distributeurs
        {
            name: "Distributeur Sécurité Plus",
            city: "Fès",
            type: "distributor",
            lat: 34.0181,
            lng: -5.0078,
            info: "Distributeur régional - Couverture Nord-Est"
        },
        {
            name: "Sécurité Atlas",
            city: "Agadir",
            type: "distributor",
            lat: 30.4278,
            lng: -9.5981,
            info: "Distributeur Sud - Spécialisé hôtellerie"
        },
        {
            name: "Fire Safety Maroc",
            city: "Meknès",
            type: "distributor",
            lat: 33.8935,
            lng: -5.5473,
            info: "Distributeur centre - Formation incluse"
        },

        // Prospects
        {
            name: "Complexe Industriel Kenitra",
            city: "Kenitra",
            type: "prospect",
            lat: 34.2610,
            lng: -6.5802,
            info: "Prospect - Négociation en cours"
        },
        {
            name: "Université Hassan II",
            city: "Casablanca",
            type: "prospect",
            lat: 33.5024,
            lng: -7.6291,
            info: "Prospect éducation - Appel d'offres"
        },
        {
            name: "Aéroport Mohammed V",
            city: "Casablanca",
            type: "prospect",
            lat: 33.3676,
            lng: -7.5897,
            info: "Prospect infrastructure - Étude en cours"
        },

        // Partenaires
        {
            name: "Formation Sécurité Maroc",
            city: "Salé",
            type: "partner",
            lat: 34.0531,
            lng: -6.7985,
            info: "Partenaire formation - Certifications"
        },
        {
            name: "Maintenance Express",
            city: "Oujda",
            type: "partner",
            lat: 34.6814,
            lng: -1.9086,
            info: "Partenaire maintenance - Région orientale"
        },
        {
            name: "Sécurité Sahara",
            city: "Laâyoune",
            type: "partner",
            lat: 27.1253,
            lng: -13.1625,
            info: "Partenaire Sud - Provinces sahariennes"
        }
    ];

    // Add markers to the map
    locations.forEach(function(location) {
        var icon;
        var color;

        switch(location.type) {
            case 'client':
                icon = clientIcon;
                color = '#dc3545';
                break;
            case 'distributor':
                icon = distributorIcon;
                color = '#28a745';
                break;
            case 'prospect':
                icon = prospectIcon;
                color = '#ffc107';
                break;
            case 'partner':
                icon = partnerIcon;
                color = '#17a2b8';
                break;
        }

        var marker = L.marker([location.lat, location.lng], {icon: icon}).addTo(map);

        var popupContent = `
            <div style="min-width: 200px;">
                <h6 style="color: ${color}; margin-bottom: 8px;">
                    <i class="fas fa-map-marker-alt me-1"></i>${location.name}
                </h6>
                <p style="margin-bottom: 5px;">
                    <strong>Ville:</strong> ${location.city}
                </p>
                <p style="margin-bottom: 5px;">
                    <strong>Type:</strong>
                    <span class="badge" style="background-color: ${color};">
                        ${location.type === 'client' ? 'Client' :
                          location.type === 'distributor' ? 'Distributeur' :
                          location.type === 'prospect' ? 'Prospect' : 'Partenaire'}
                    </span>
                </p>
                <p style="margin-bottom: 0; font-size: 0.9em;">
                    ${location.info}
                </p>
            </div>
        `;

        marker.bindPopup(popupContent);
    });

    // Add a scale control
    L.control.scale().addTo(map);

    // Add fullscreen control (optional)
    map.on('click', function() {
        // You can add click events here if needed
    });
});
</script>
{% endblock %}
