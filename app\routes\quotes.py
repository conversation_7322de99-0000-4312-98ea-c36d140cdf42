from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app import db
from app.models import Quote, QuoteItem, Product, Client, Invoice, DeliveryNote, InvoiceItem, DeliveryNoteItem
from app.forms.quote_forms import <PERSON>uo<PERSON><PERSON><PERSON>, Quote<PERSON>temForm
from datetime import datetime, timedelta
import random
import string

bp = Blueprint('quotes', __name__)

def sync_quote_items_to_related_documents(quote, new_item=None):
    """Synchronize quote items with related invoice and delivery note"""
    try:
        # Find related invoice
        invoice = Invoice.query.filter_by(quote_id=quote.id).first()
        if invoice:
            if new_item:
                # Add the new item to invoice
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=new_item.product_id,
                    description=new_item.description,
                    quantity=new_item.quantity,
                    unit_price=new_item.unit_price,
                    total=new_item.total
                )
                db.session.add(invoice_item)

                # Find related delivery note
                delivery_note = DeliveryNote.query.filter_by(invoice_id=invoice.id).first()
                if delivery_note:
                    # Add the new item to delivery note
                    delivery_note_item = DeliveryNoteItem(
                        delivery_note_id=delivery_note.id,
                        product_id=new_item.product_id,
                        description=new_item.description,
                        quantity=new_item.quantity
                    )
                    db.session.add(delivery_note_item)

            # Recalculate invoice totals
            invoice.calculate_totals()
            db.session.flush()

    except Exception as e:
        print(f"DEBUG: Error syncing items: {str(e)}")

def sync_quote_item_removal(quote, removed_item):
    """Remove corresponding items from related invoice and delivery note"""
    try:
        # Find related invoice
        invoice = Invoice.query.filter_by(quote_id=quote.id).first()
        if invoice:
            # Find and remove corresponding invoice item
            invoice_item = InvoiceItem.query.filter_by(
                invoice_id=invoice.id,
                product_id=removed_item.product_id,
                description=removed_item.description
            ).first()
            if invoice_item:
                db.session.delete(invoice_item)

            # Find related delivery note
            delivery_note = DeliveryNote.query.filter_by(invoice_id=invoice.id).first()
            if delivery_note:
                # Find and remove corresponding delivery note item
                delivery_note_item = DeliveryNoteItem.query.filter_by(
                    delivery_note_id=delivery_note.id,
                    product_id=removed_item.product_id,
                    description=removed_item.description
                ).first()
                if delivery_note_item:
                    db.session.delete(delivery_note_item)

            # Recalculate invoice totals
            invoice.calculate_totals()
            db.session.flush()

    except Exception as e:
        print(f"DEBUG: Error removing synced items: {str(e)}")

def generate_quote_number():
    prefix = "DEV-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

@bp.route('/')
def index():
    quotes = Quote.query.order_by(Quote.date.desc()).all()
    return render_template('quotes/index.html', quotes=quotes)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    form = QuoteForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    if form.validate_on_submit():
        try:
            # Create quote first
            quote = Quote(
                quote_number=generate_quote_number(),
                client_id=form.client_id.data,
                date=form.date.data or datetime.utcnow(),
                expiration_date=form.expiration_date.data or (datetime.utcnow() + timedelta(days=30)),
                status=form.status.data,
                tax_rate=form.tax_rate.data,
                notes=form.notes.data
            )

            db.session.add(quote)
            db.session.commit()

            # Create Invoice automatically
            invoice_number = f"FAC-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
            invoice = Invoice(
                invoice_number=invoice_number,
                client_id=quote.client_id,
                quote_id=quote.id,
                date=quote.date,
                due_date=quote.expiration_date,
                status='draft',
                tax_rate=quote.tax_rate,
                notes=f"Facture générée automatiquement à partir du devis {quote.quote_number}"
            )
            db.session.add(invoice)
            db.session.commit()

            # Copy quote items to invoice (if any exist)
            for quote_item in quote.items:
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    description=quote_item.description,
                    quantity=quote_item.quantity,
                    unit_price=quote_item.unit_price,
                    total=quote_item.quantity * quote_item.unit_price
                )
                db.session.add(invoice_item)

            # Calculate invoice totals
            invoice.calculate_totals()
            db.session.commit()

            # Create Delivery Note automatically
            delivery_note_number = f"BL-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
            delivery_note = DeliveryNote(
                delivery_note_number=delivery_note_number,
                client_id=quote.client_id,
                invoice_id=invoice.id,
                date=quote.date,
                status='draft',
                notes=f"Bon de livraison généré automatiquement à partir du devis {quote.quote_number}"
            )
            db.session.add(delivery_note)
            db.session.commit()

            # Copy quote items to delivery note (without prices)
            for quote_item in quote.items:
                delivery_item = DeliveryNoteItem(
                    delivery_note_id=delivery_note.id,
                    description=quote_item.description,
                    quantity=quote_item.quantity
                )
                db.session.add(delivery_item)

            db.session.commit()

            flash(f'Devis créé avec succès! Facture ({invoice.invoice_number}) et Bon de livraison ({delivery_note.delivery_note_number}) générés automatiquement.', 'success')
            return redirect(url_for('quotes.edit', id=quote.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
            return render_template('quotes/create.html', form=form)

    # Get company information for the header
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('quotes/create.html', form=form, company=company)

@bp.route('/create-interactive', methods=['GET', 'POST'])
def create_interactive():
    """Interactive quote creation with document-like interface"""
    from datetime import datetime, timedelta
    import json

    if request.method == 'POST':
        # Handle AJAX request for saving quote
        try:
            data = request.get_json()

            # Validate required fields
            if not data.get('client_id'):
                return jsonify({'success': False, 'message': 'Veuillez sélectionner un client'})

            if not data.get('items') or len(data.get('items', [])) == 0:
                return jsonify({'success': False, 'message': 'Veuillez ajouter au moins un produit'})

            # Create new quote
            quote = Quote(
                quote_number=generate_quote_number(),
                client_id=int(data.get('client_id')),
                date=datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else datetime.utcnow(),
                expiration_date=datetime.strptime(data.get('valid_until'), '%Y-%m-%d') if data.get('valid_until') else (datetime.utcnow() + timedelta(days=30)),
                status='draft',
                tax_rate=20.0,
                notes=data.get('object', '')
            )

            db.session.add(quote)
            db.session.flush()  # Get the quote ID

            # Add items
            for item_data in data.get('items', []):
                item = QuoteItem(
                    quote_id=quote.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity'),
                    unit_price=item_data.get('unit_price'),
                    total=item_data.get('quantity') * item_data.get('unit_price')
                )
                db.session.add(item)

            db.session.commit()

            # Calculate totals
            quote.calculate_totals()
            db.session.commit()

            # NOW CREATE RELATED DOCUMENTS AUTOMATICALLY
            try:
                # Create Invoice automatically
                invoice_number = f"FAC-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
                invoice = Invoice(
                    invoice_number=invoice_number,
                    client_id=quote.client_id,
                    quote_id=quote.id,
                    date=quote.date,
                    due_date=quote.expiration_date,
                    status='draft',
                    tax_rate=quote.tax_rate,
                    notes=f"Facture générée automatiquement à partir du devis {quote.quote_number}"
                )
                db.session.add(invoice)
                db.session.commit()

                # Copy quote items to invoice
                for quote_item in quote.items:
                    invoice_item = InvoiceItem(
                        invoice_id=invoice.id,
                        description=quote_item.description,
                        quantity=quote_item.quantity,
                        unit_price=quote_item.unit_price,
                        total=quote_item.quantity * quote_item.unit_price
                    )
                    db.session.add(invoice_item)

                # Calculate invoice totals
                invoice.calculate_totals()
                db.session.commit()

                # Create Delivery Note automatically
                delivery_note_number = f"BL-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
                delivery_note = DeliveryNote(
                    delivery_note_number=delivery_note_number,
                    client_id=quote.client_id,
                    invoice_id=invoice.id,
                    date=quote.date,
                    status='draft',
                    notes=f"Bon de livraison généré automatiquement à partir du devis {quote.quote_number}"
                )
                db.session.add(delivery_note)
                db.session.commit()

                # Copy quote items to delivery note (without prices)
                for quote_item in quote.items:
                    delivery_item = DeliveryNoteItem(
                        delivery_note_id=delivery_note.id,
                        description=quote_item.description,
                        quantity=quote_item.quantity
                    )
                    db.session.add(delivery_item)

                db.session.commit()

                return jsonify({
                    'success': True,
                    'quote_id': quote.id,
                    'message': f'Devis créé avec succès! Facture ({invoice.invoice_number}) et Bon de livraison ({delivery_note.delivery_note_number}) générés automatiquement.'
                })

            except Exception as e:
                # If related documents fail, rollback and still return success for quote
                db.session.rollback()
                # Re-commit just the quote
                db.session.add(quote)
                db.session.commit()
                return jsonify({
                    'success': True,
                    'quote_id': quote.id,
                    'message': f'Devis créé avec succès, mais erreur lors de la création des documents liés: {str(e)}'
                })

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    # Generate quote number and dates
    quote_number = generate_quote_number()
    today = datetime.now().strftime('%Y-%m-%d')
    valid_until = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    return render_template('quotes/create_interactive.html',
                         clients=clients,
                         company=company,
                         quote_number=quote_number,
                         today=today,
                         valid_until=valid_until)

@bp.route('/<int:id>/edit-interactive', methods=['GET', 'POST'])
def edit_interactive(id):
    """Interactive quote editing with document-like interface"""
    quote = Quote.query.get_or_404(id)

    if request.method == 'POST':
        # Handle AJAX request for updating quote
        try:
            data = request.get_json()

            # Update quote
            quote.client_id = data.get('client_id')
            quote.date = datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else quote.date
            quote.expiration_date = datetime.strptime(data.get('valid_until'), '%Y-%m-%d') if data.get('valid_until') else quote.expiration_date
            quote.notes = data.get('object', '')

            # Delete existing items
            QuoteItem.query.filter_by(quote_id=quote.id).delete()

            # Add new items
            for item_data in data.get('items', []):
                item = QuoteItem(
                    quote_id=quote.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity'),
                    unit_price=item_data.get('unit_price'),
                    total=item_data.get('quantity') * item_data.get('unit_price')
                )
                db.session.add(item)

            db.session.commit()

            # Calculate totals
            quote.calculate_totals()
            db.session.commit()

            return jsonify({'success': True, 'quote_id': quote.id})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('quotes/edit_interactive.html',
                         quote=quote,
                         clients=clients,
                         company=company)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    quote = Quote.query.get_or_404(id)
    form = QuoteForm(obj=quote)
    item_form = QuoteItemForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    # Populate product choices for the item form
    item_form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        quote.client_id = form.client_id.data
        quote.date = form.date.data
        quote.expiration_date = form.expiration_date.data
        quote.status = form.status.data
        quote.tax_rate = form.tax_rate.data
        quote.notes = form.notes.data

        db.session.commit()
        quote.calculate_totals()
        db.session.commit()

        flash('Quote updated successfully!', 'success')
        return redirect(url_for('quotes.edit', id=quote.id))

    return render_template('quotes/edit.html', quote=quote, form=form, item_form=item_form)

@bp.route('/<int:id>/add_item', methods=['POST'])
def add_item(id):
    quote = Quote.query.get_or_404(id)
    form = QuoteItemForm()

    # Populate product choices
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        product = Product.query.get(form.product_id.data)

        item = QuoteItem(
            quote_id=quote.id,
            product_id=form.product_id.data,
            quantity=form.quantity.data,
            unit_price=form.unit_price.data or product.unit_price,
            total=form.quantity.data * (form.unit_price.data or product.unit_price)
        )

        db.session.add(item)

        # Auto-sync with related documents
        sync_quote_items_to_related_documents(quote, item)

        db.session.commit()

        # Recalculate quote totals
        quote.calculate_totals()
        db.session.commit()

        flash('Produit ajouté au devis et synchronisé avec les documents liés!', 'success')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"{getattr(form, field).label.text}: {error}", 'danger')

    return redirect(url_for('quotes.edit', id=quote.id))

@bp.route('/<int:quote_id>/remove_item/<int:item_id>', methods=['POST'])
def remove_item(quote_id, item_id):
    item = QuoteItem.query.get_or_404(item_id)
    quote = Quote.query.get_or_404(quote_id)

    # Remove from related documents first
    sync_quote_item_removal(quote, item)

    db.session.delete(item)
    db.session.commit()

    # Recalculate quote totals
    quote.calculate_totals()
    db.session.commit()

    flash('Produit retiré du devis et des documents liés!', 'success')
    return redirect(url_for('quotes.edit', id=quote_id))

@bp.route('/<int:id>')
def show(id):
    """Show quote using the same interactive template as create/edit"""
    quote = Quote.query.get_or_404(id)

    try:
        from app.models.company import Company
        from app.models import Client

        # Get company information
        company = Company.query.first()

        # Get all clients for the dropdown
        clients = Client.query.all()

        # Render the interactive template (same as create/edit but read-only)
        return render_template('quotes/print_final.html',
                             quote=quote,
                             company=company,
                             clients=clients)
    except Exception as e:
        flash(f'Erreur lors de l\'affichage du devis: {str(e)}', 'danger')
        return redirect(url_for('quotes.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    quote = Quote.query.get_or_404(id)
    db.session.delete(quote)
    db.session.commit()
    flash('Quote deleted successfully!', 'success')
    return redirect(url_for('quotes.index'))

@bp.route('/<int:id>/generate_pdf')
def generate_pdf(id):
    """Display HTML template for printing (same as create form)"""
    quote = Quote.query.get_or_404(id)

    try:
        from app.models.company import Company

        # Get company information
        company = Company.query.first()

        # Render the HTML template directly (same as interactive form)
        return render_template('quotes/print_final.html', quote=quote, company=company)

    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('quotes.show', id=quote.id))

@bp.route('/<int:id>/view_pdf')
def view_pdf(id):
    """View PDF template in browser"""
    quote = Quote.query.get_or_404(id)

    try:
        from app.models.company import Company
        company = Company.query.first()
        return render_template('quotes/print_final.html', quote=quote, company=company)
    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('quotes.show', id=quote.id))
