from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from config import Config

db = SQLAlchemy()
migrate = Migrate()
login = LoginManager()
login.login_view = 'auth.login'

# User loader will be defined after models are imported

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    db.init_app(app)
    migrate.init_app(app, db)
    login.init_app(app)

    # Add custom filters
    @app.template_filter('nl2br')
    def nl2br_filter(s):
        if s is None:
            return ""
        return s.replace('\n', '<br>')

    # Register blueprints
    from app.routes.main import bp as main_bp
    app.register_blueprint(main_bp)

    try:
        from app.routes.users import bp as users_bp
        app.register_blueprint(users_bp, url_prefix='/users')
    except ImportError:
        print("Warning: users blueprint not found")

    try:
        from app.routes.company import bp as company_bp
        app.register_blueprint(company_bp, url_prefix='/company')
    except ImportError:
        print("Warning: company blueprint not found")

    # Register all blueprints
    try:
        from app.routes.products import bp as products_bp
        app.register_blueprint(products_bp, url_prefix='/products')
    except ImportError:
        print("Warning: products blueprint not found")

    try:
        from app.routes.stock import bp as stock_bp
        app.register_blueprint(stock_bp, url_prefix='/stock')
    except ImportError:
        print("Warning: stock blueprint not found")

    try:
        from app.routes.quotes import bp as quotes_bp
        app.register_blueprint(quotes_bp, url_prefix='/quotes')
    except ImportError:
        print("Warning: quotes blueprint not found")

    try:
        from app.routes.invoices import bp as invoices_bp
        app.register_blueprint(invoices_bp, url_prefix='/invoices')
    except ImportError:
        print("Warning: invoices blueprint not found")

    try:
        from app.routes.delivery_notes import bp as delivery_notes_bp
        app.register_blueprint(delivery_notes_bp, url_prefix='/delivery-notes')
    except ImportError:
        print("Warning: delivery_notes blueprint not found")

    try:
        from app.routes.clients import bp as clients_bp
        app.register_blueprint(clients_bp, url_prefix='/clients')
    except ImportError:
        print("Warning: clients blueprint not found")

    try:
        from app.routes.suppliers import suppliers_bp
        app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    except ImportError:
        print("Warning: suppliers blueprint not found")

    try:
        from app.routes.auth import bp as auth_bp
        app.register_blueprint(auth_bp)
    except ImportError:
        print("Warning: auth blueprint not found")

    try:
        from app.auth.password_routes import password_bp
        app.register_blueprint(password_bp, url_prefix='/auth')
    except ImportError:
        print("Warning: password blueprint not found")

    try:
        from app.routes.admin import bp as admin_bp
        app.register_blueprint(admin_bp)
    except ImportError:
        print("Warning: admin blueprint not found")

    @app.route('/test')
    def test_page():
        return 'The application is working!'

    # Register context processor for company information
    @app.context_processor
    def inject_company():
        from app.models.company import Company
        try:
            company = Company.query.first()
        except:
            company = None
        return dict(global_company=company)

    # Import models to ensure they are registered with SQLAlchemy
    from app import models

    # Set up user loader after app is created
    @login.user_loader
    def load_user(id):
        from app.models import User
        return User.query.get(int(id))

    return app
