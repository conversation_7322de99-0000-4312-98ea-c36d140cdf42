from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, EmailField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from app.models import User

class RequestPasswordResetForm(FlaskForm):
    """Form for requesting password reset"""
    email = EmailField('Adresse Email', 
                      validators=[DataRequired(message='L\'adresse email est requise'), 
                                Email(message='Veuillez entrer une adresse email valide')])
    submit = SubmitField('Envoyer le lien de réinitialisation')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is None:
            raise ValidationError('Aucun compte n\'est associé à cette adresse email.')

class ResetPasswordForm(FlaskForm):
    """Form for resetting password with token"""
    password = PasswordField('Nouveau mot de passe', 
                            validators=[DataRequired(message='Le mot de passe est requis'),
                                      Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')])
    password2 = PasswordField('Confirmer le nouveau mot de passe',
                             validators=[DataRequired(message='La confirmation du mot de passe est requise'),
                                       EqualTo('password', message='Les mots de passe doivent correspondre')])
    submit = SubmitField('Réinitialiser le mot de passe')

class ChangePasswordForm(FlaskForm):
    """Form for changing password when logged in"""
    current_password = PasswordField('Mot de passe actuel',
                                   validators=[DataRequired(message='Le mot de passe actuel est requis')])
    new_password = PasswordField('Nouveau mot de passe',
                               validators=[DataRequired(message='Le nouveau mot de passe est requis'),
                                         Length(min=6, message='Le mot de passe doit contenir au moins 6 caractères')])
    confirm_password = PasswordField('Confirmer le nouveau mot de passe',
                                   validators=[DataRequired(message='La confirmation du mot de passe est requise'),
                                             EqualTo('new_password', message='Les mots de passe doivent correspondre')])
    submit = SubmitField('Changer le mot de passe')
