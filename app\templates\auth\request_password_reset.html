{% extends "base.html" %}

{% block title %}Mot de passe oublié{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-danger text-white text-center py-4">
                <h3 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    Mot de passe oublié
                </h3>
                <p class="mb-0 mt-2">Réinitialisation de votre mot de passe</p>
            </div>
            
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-lock text-danger" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">
                        Entrez votre adresse email pour recevoir un lien de réinitialisation de votre mot de passe.
                    </p>
                </div>

                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        {{ form.email.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-envelope text-danger"></i>
                            </span>
                            {{ form.email(class="form-control form-control-lg", placeholder="<EMAIL>") }}
                        </div>
                        {% if form.email.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.email.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 mb-4">
                        {{ form.submit(class="btn btn-danger btn-lg", id="submitBtn") }}
                    </div>
                </form>

                <div class="text-center">
                    <hr class="my-4">
                    <p class="text-muted mb-3">
                        <i class="fas fa-info-circle me-1"></i>
                        Vous vous souvenez de votre mot de passe ?
                    </p>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        Retour à la connexion
                    </a>
                </div>
            </div>
        </div>

        <!-- Information Card -->
        <div class="card mt-4 border-info">
            <div class="card-body">
                <h6 class="card-title text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    Instructions
                </h6>
                <ul class="list-unstyled mb-0 small text-muted">
                    <li><i class="fas fa-check text-success me-2"></i>Vérifiez votre boîte de réception</li>
                    <li><i class="fas fa-check text-success me-2"></i>Cliquez sur le lien reçu par email</li>
                    <li><i class="fas fa-check text-success me-2"></i>Créez un nouveau mot de passe sécurisé</li>
                    <li><i class="fas fa-check text-success me-2"></i>Le lien expire après 1 heure</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Envoi en cours...';
        
        // Re-enable after 5 seconds in case of error
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 5000);
    });
});
</script>
{% endblock %}
