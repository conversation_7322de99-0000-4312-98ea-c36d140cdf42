/**
 * ULTIMATE BUTTON FIX
 * The final solution that will definitely work
 */

console.log('🚀 Loading Ultimate Button Fix...');

// Global state
window.buttonFixLoaded = true;

// Ultimate delete function
window.ultimateDelete = function(button) {
    console.log('🗑️ Ultimate delete called:', button);
    
    try {
        // Get all possible info
        const itemName = extractItemName(button);
        const deleteUrl = extractDeleteUrl(button);
        const itemType = extractItemType(button);
        
        console.log('Delete data:', { itemName, deleteUrl, itemType });
        
        if (!deleteUrl) {
            alert('❌ Erreur: URL de suppression non trouvée.');
            return false;
        }
        
        // Simple confirm dialog
        const confirmMessage = `⚠️ SUPPRESSION ⚠️\n\nSupprimer ${itemType} :\n"${itemName}"\n\n❌ Action irréversible ❌\n\nConfirmer ?`;
        
        if (confirm(confirmMessage)) {
            console.log('✅ Deletion confirmed');
            
            // Create form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            form.style.display = 'none';
            
            // Add CSRF if available
            const csrfToken = getCSRFToken();
            if (csrfToken) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'csrf_token';
                input.value = csrfToken;
                form.appendChild(input);
            }
            
            // Submit
            document.body.appendChild(form);
            form.submit();
            return true;
        }
        
        return false;
        
    } catch (error) {
        console.error('Delete error:', error);
        alert('❌ Erreur lors de la suppression: ' + error.message);
        return false;
    }
};

// Ultimate close function
window.ultimateClose = function() {
    console.log('🚪 Ultimate close called');
    
    try {
        // Method 1: Close popup
        if (window.opener && !window.opener.closed) {
            console.log('Closing popup');
            window.close();
            return;
        }
        
        // Method 2: History back
        if (history.length > 1) {
            console.log('Going back');
            history.back();
            return;
        }
        
        // Method 3: Smart redirect
        const path = window.location.pathname;
        let target = '/';
        
        if (path.includes('invoice')) target = '/invoices/';
        else if (path.includes('quote')) target = '/quotes/';
        else if (path.includes('delivery')) target = '/delivery-notes/';
        else if (path.includes('client')) target = '/clients/';
        else if (path.includes('supplier')) target = '/suppliers/';
        else if (path.includes('product')) target = '/products/';
        
        console.log('Redirecting to:', target);
        window.location.href = target;
        
    } catch (error) {
        console.error('Close error:', error);
        window.location.href = '/';
    }
};

// Extract functions
function extractItemName(button) {
    // Try data attribute
    let name = button.getAttribute('data-item-name');
    if (name) return name;
    
    // Try table row
    const row = button.closest('tr');
    if (row) {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            name = cells[1].textContent.trim();
            if (name) return name;
        }
    }
    
    // Try card title
    const card = button.closest('.card');
    if (card) {
        const title = card.querySelector('.card-title, h1, h2, h3, h4, h5');
        if (title) {
            name = title.textContent.trim();
            if (name) return name;
        }
    }
    
    // Try nearby text
    const parent = button.parentElement;
    if (parent) {
        const text = parent.textContent.trim();
        if (text && text.length < 100) {
            return text.replace(/\s+/g, ' ');
        }
    }
    
    return 'cet élément';
}

function extractDeleteUrl(button) {
    // Try data attribute
    let url = button.getAttribute('data-delete-url');
    if (url) return url;
    
    // Try href
    if (button.tagName === 'A') {
        url = button.href;
        if (url && url !== '#') return url;
    }
    
    // Try form action
    const form = button.closest('form');
    if (form && form.action) {
        return form.action;
    }
    
    // Try onclick
    const onclick = button.getAttribute('onclick');
    if (onclick) {
        const match = onclick.match(/['"]([^'"]*delete[^'"]*)['"]/i);
        if (match) return match[1];
    }
    
    // Try parent links
    const parentLink = button.closest('a');
    if (parentLink && parentLink.href) {
        return parentLink.href;
    }
    
    return null;
}

function extractItemType(button) {
    const url = extractDeleteUrl(button);
    if (!url) return 'élément';
    
    if (url.includes('client')) return 'le client';
    if (url.includes('supplier') || url.includes('fournisseur')) return 'le fournisseur';
    if (url.includes('quote') || url.includes('devis')) return 'le devis';
    if (url.includes('invoice') || url.includes('facture')) return 'la facture';
    if (url.includes('delivery') || url.includes('livraison')) return 'le bon de livraison';
    if (url.includes('product') || url.includes('produit')) return 'le produit';
    if (url.includes('equipment')) return 'l\'équipement';
    if (url.includes('user') || url.includes('utilisateur')) return 'l\'utilisateur';
    
    return 'cet élément';
}

function getCSRFToken() {
    // Try meta tag
    const meta = document.querySelector('meta[name="csrf-token"]');
    if (meta) return meta.getAttribute('content');
    
    // Try hidden input
    const input = document.querySelector('input[name="csrf_token"]');
    if (input) return input.value;
    
    return null;
}

// Fix all buttons on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Ultimate fix starting...');
    
    setTimeout(function() {
        fixAllButtons();
    }, 100);
    
    // Also fix on dynamic content
    const observer = new MutationObserver(function() {
        fixAllButtons();
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

function fixAllButtons() {
    console.log('🔧 Fixing all buttons...');
    
    // Fix delete buttons
    const deleteSelectors = [
        '.btn-danger',
        '.btn-outline-danger',
        'button[class*="delete"]',
        'a[class*="delete"]',
        'button[onclick*="delete"]',
        'a[onclick*="delete"]',
        'button[onclick*="supprimer"]',
        'a[onclick*="supprimer"]'
    ];
    
    deleteSelectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(button => {
            if (!button.hasAttribute('data-fixed')) {
                button.setAttribute('data-fixed', 'true');
                button.removeAttribute('onclick');
                
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    ultimateDelete(this);
                });
                
                console.log('Fixed delete button:', button);
            }
        });
    });
    
    // Fix close buttons
    const closeSelectors = [
        '.btn-secondary',
        'button[onclick*="close"]',
        'button[onclick*="fermer"]',
        'button[onclick*="goBack"]',
        'a[onclick*="close"]',
        'a[onclick*="fermer"]',
        'a[onclick*="goBack"]'
    ];
    
    closeSelectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(button => {
            // Skip modal dismiss buttons
            if (button.hasAttribute('data-bs-dismiss')) return;
            
            if (!button.hasAttribute('data-fixed')) {
                button.setAttribute('data-fixed', 'true');
                button.removeAttribute('onclick');
                
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    ultimateClose();
                });
                
                console.log('Fixed close button:', button);
            }
        });
    });
}

// Global aliases
window.deleteItem = window.ultimateDelete;
window.closeWindow = window.ultimateClose;
window.fermer = window.ultimateClose;
window.goBack = window.ultimateClose;

console.log('✅ Ultimate Button Fix loaded!');
