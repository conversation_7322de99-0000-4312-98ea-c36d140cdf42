{% extends 'base.html' %}

{% block title %}Modifier Utilisateur: {{ user.username }} - Gestion d'Extincteurs{% endblock %}

{% block head %}
<style>
/* ===== PROFESSIONAL PERMISSIONS INTERFACE ===== */

.permissions-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
    position: relative;
}

.permissions-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.permissions-header {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: none;
    padding: 20px 25px;
    color: white;
}

.permissions-header h6 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.toggle-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 25px;
    padding: 8px 16px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.toggle-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    transform: scale(1.05);
}

.permissions-body {
    background: white;
    padding: 25px;
}

.permissions-alert {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 25px;
    border-left: 4px solid #2196f3;
    box-shadow: 0 2px 10px rgba(33,150,243,0.1);
}

.permissions-alert .alert-icon {
    color: #2196f3;
    font-size: 1.2rem;
    margin-right: 10px;
}

.control-buttons {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.control-btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: none;
    margin-right: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn.select-all {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    box-shadow: 0 4px 15px rgba(76,175,80,0.3);
}

.control-btn.select-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76,175,80,0.4);
}

.control-btn.clear-all {
    background: linear-gradient(135deg, #757575, #616161);
    color: white;
    box-shadow: 0 4px 15px rgba(117,117,117,0.3);
}

.control-btn.clear-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(117,117,117,0.4);
}

.permission-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 100%;
    position: relative;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.permission-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--card-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.permission-card:hover::before {
    opacity: 1;
}

.permission-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.permission-card.products { --card-gradient: linear-gradient(135deg, #2196f3, #21cbf3); }
.permission-card.stock { --card-gradient: linear-gradient(135deg, #00bcd4, #4dd0e1); }
.permission-card.clients { --card-gradient: linear-gradient(135deg, #4caf50, #8bc34a); }
.permission-card.quotes { --card-gradient: linear-gradient(135deg, #ff9800, #ffc107); }
.permission-card.invoices { --card-gradient: linear-gradient(135deg, #f44336, #e91e63); }
.permission-card.delivery { --card-gradient: linear-gradient(135deg, #9e9e9e, #607d8b); }
.permission-card.reports { --card-gradient: linear-gradient(135deg, #424242, #212121); }

.card-header-custom {
    padding: 20px;
    border: none;
    position: relative;
    overflow: hidden;
}

.card-header-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--card-gradient);
    opacity: 0.9;
}

.card-header-custom * {
    position: relative;
    z-index: 1;
}

.card-header-custom h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card-header-custom .header-icon {
    font-size: 1.2rem;
    margin-right: 10px;
    opacity: 0.9;
}

.card-body-custom {
    padding: 20px;
    background: white;
}

.permission-check {
    margin-bottom: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.permission-check:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.permission-check-input {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.permission-check-input:checked {
    background: var(--card-gradient);
    border-color: transparent;
    transform: scale(1.1);
}

.permission-check-input:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.permission-check-label {
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    color: #333;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
}

.permission-check-label:hover {
    color: #007bff;
}

.permission-check-label .label-icon {
    margin-right: 8px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.permission-check:hover .label-icon {
    opacity: 1;
}

#permissionsContent {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.permission-stats {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    border: 1px solid #dee2e6;
}

.stats-item {
    text-align: center;
    padding: 10px;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.stats-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .permission-card {
        margin-bottom: 20px;
    }

    .permissions-body {
        padding: 15px;
    }

    .control-btn {
        margin-bottom: 10px;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user-edit me-2"></i>Modifier Utilisateur: {{ user.username }}
    </h1>
    <div>
        <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations de l'utilisateur</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('users.edit', id=user.id) }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label required">Nom d'utilisateur</label>
                        {{ form.username(class="form-control") }}
                        {% if form.username.errors %}
                            <div class="text-danger">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label required">Email</label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Nom complet</label>
                        {{ form.full_name(class="form-control") }}
                        {% if form.full_name.errors %}
                            <div class="text-danger">
                                {% for error in form.full_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="is_admin" class="form-label">Rôle</label>
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="form-check form-switch">
                                    {{ form.is_admin(class="form-check-input", id="is_admin") }}
                                    <label class="form-check-label fw-bold" for="is_admin">
                                        <i class="fas fa-user-shield me-2 text-primary"></i>Administrateur
                                    </label>
                                </div>
                                <small class="form-text text-muted">Les administrateurs ont accès à toutes les fonctionnalités.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password" class="form-label">Nouveau mot de passe</label>
                        {{ form.password(class="form-control") }}
                        {% if form.password.errors %}
                            <div class="text-danger">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password2" class="form-label">Confirmer le mot de passe</label>
                        {{ form.password2(class="form-control") }}
                        {% if form.password2.errors %}
                            <div class="text-danger">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Professional Permissions Section -->
            <div class="mb-4" id="permissionsSection">
                <div class="permissions-container">
                    <div class="permissions-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6>
                                <i class="fas fa-shield-alt me-2"></i>Permissions et Contrôle d'Accès
                            </h6>
                            <button type="button" class="toggle-btn" onclick="togglePermissions()">
                                <i class="fas fa-chevron-down" id="toggleIcon"></i> <span id="toggleText">Afficher</span>
                            </button>
                        </div>
                    </div>
                    <div class="permissions-body" id="permissionsContent" style="display: none;">
                        <div class="permissions-alert">
                            <i class="fas fa-info-circle alert-icon"></i>
                            <strong>Information:</strong> Les administrateurs bénéficient automatiquement de tous les privilèges d'accès.
                        </div>

                        <div class="control-buttons">
                            <button type="button" class="control-btn select-all" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double me-2"></i>Tout sélectionner
                            </button>
                            <button type="button" class="control-btn clear-all" onclick="clearAllPermissions()">
                                <i class="fas fa-times me-2"></i>Tout désélectionner
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="permission-card products">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-box header-icon"></i>Gestion Produits</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_products(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_products">
                                                <i class="fas fa-eye label-icon"></i>Consulter le catalogue
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_products(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_products">
                                                <i class="fas fa-edit label-icon"></i>Modifier les produits
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="permission-card stock">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-warehouse header-icon"></i>Gestion Stock</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_stock(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_stock">
                                                <i class="fas fa-eye label-icon"></i>Consulter l'inventaire
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_stock(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_stock">
                                                <i class="fas fa-edit label-icon"></i>Gérer les mouvements
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="permission-card clients">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-users header-icon"></i>Gestion Clients</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_clients(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_clients">
                                                <i class="fas fa-eye label-icon"></i>Consulter la clientèle
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_clients(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_clients">
                                                <i class="fas fa-edit label-icon"></i>Modifier les données
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="permission-card quotes">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-file-alt header-icon"></i>Devis & Estimations</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_quotes(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_quotes">
                                                <i class="fas fa-eye label-icon"></i>Consulter les devis
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_quotes(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_quotes">
                                                <i class="fas fa-edit label-icon"></i>Créer & modifier
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="permission-card invoices">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-file-invoice header-icon"></i>Facturation</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_invoices(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_invoices">
                                                <i class="fas fa-eye label-icon"></i>Consulter les factures
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_invoices(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_invoices">
                                                <i class="fas fa-edit label-icon"></i>Gérer la facturation
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="permission-card delivery">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-truck header-icon"></i>Livraisons</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_view_delivery_notes(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_view_delivery_notes">
                                                <i class="fas fa-eye label-icon"></i>Consulter les BL
                                            </label>
                                        </div>
                                        <div class="permission-check">
                                            {{ form.can_edit_delivery_notes(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_edit_delivery_notes">
                                                <i class="fas fa-edit label-icon"></i>Gérer les livraisons
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="permission-card reports">
                                    <div class="card-header-custom">
                                        <h6><i class="fas fa-chart-bar header-icon"></i>Rapports & Analytics</h6>
                                    </div>
                                    <div class="card-body-custom">
                                        <div class="permission-check">
                                            {{ form.can_print_reports(class="permission-check-input") }}
                                            <label class="permission-check-label" for="can_print_reports">
                                                <i class="fas fa-print label-icon"></i>Générer les rapports
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Statistics -->
                        <div class="permission-stats">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="stats-item">
                                        <div class="stats-number" id="selectedCount">0</div>
                                        <div class="stats-label">Permissions Activées</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stats-item">
                                        <div class="stats-number" id="totalCount">13</div>
                                        <div class="stats-label">Total Permissions</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stats-item">
                                        <div class="stats-number" id="progressPercent">0%</div>
                                        <div class="stats-label">Niveau d'Accès</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<script>
// Professional Permissions Interface Functions
function togglePermissions() {
    const content = document.getElementById('permissionsContent');
    const icon = document.getElementById('toggleIcon');
    const text = document.getElementById('toggleText');

    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        content.classList.add('fade-in');
        icon.className = 'fas fa-chevron-up';
        text.textContent = 'Masquer';

        // Update statistics when showing
        updatePermissionStats();
    } else {
        content.style.display = 'none';
        content.classList.remove('fade-in');
        icon.className = 'fas fa-chevron-down';
        text.textContent = 'Afficher';
    }
}

// تحديد جميع الصلاحيات مع تأثيرات بصرية
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-check-input');
    checkboxes.forEach((checkbox, index) => {
        if (!checkbox.disabled) {
            setTimeout(() => {
                checkbox.checked = true;
                checkbox.closest('.permission-check').style.background = '#e8f5e8';
                setTimeout(() => {
                    checkbox.closest('.permission-check').style.background = '';
                }, 300);
            }, index * 50);
        }
    });

    setTimeout(() => {
        updatePermissionStats();
    }, checkboxes.length * 50 + 100);
}

// إلغاء تحديد جميع الصلاحيات مع تأثيرات بصرية
function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-check-input');
    checkboxes.forEach((checkbox, index) => {
        if (!checkbox.disabled) {
            setTimeout(() => {
                checkbox.checked = false;
                checkbox.closest('.permission-check').style.background = '#ffe8e8';
                setTimeout(() => {
                    checkbox.closest('.permission-check').style.background = '';
                }, 300);
            }, index * 50);
        }
    });

    setTimeout(() => {
        updatePermissionStats();
    }, checkboxes.length * 50 + 100);
}

// تحديث إحصائيات الصلاحيات
function updatePermissionStats() {
    const allCheckboxes = document.querySelectorAll('.permission-check-input');
    const checkedCheckboxes = document.querySelectorAll('.permission-check-input:checked');
    const totalCount = allCheckboxes.length;
    const selectedCount = checkedCheckboxes.length;
    const percentage = Math.round((selectedCount / totalCount) * 100);

    // تحديث الأرقام مع تأثير العد
    animateNumber('selectedCount', selectedCount);
    animateNumber('totalCount', totalCount);
    animatePercentage('progressPercent', percentage);
}

// تأثير العد المتحرك للأرقام
function animateNumber(elementId, targetValue) {
    const element = document.getElementById(elementId);
    const startValue = parseInt(element.textContent) || 0;
    const duration = 500;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentValue = Math.round(startValue + (targetValue - startValue) * progress);
        element.textContent = currentValue;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// تأثير العد المتحرك للنسبة المئوية
function animatePercentage(elementId, targetValue) {
    const element = document.getElementById(elementId);
    const startValue = parseInt(element.textContent) || 0;
    const duration = 500;
    const startTime = performance.now();

    function updatePercentage(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentValue = Math.round(startValue + (targetValue - startValue) * progress);
        element.textContent = currentValue + '%';

        // تغيير لون النسبة حسب القيمة
        if (currentValue < 30) {
            element.style.color = '#dc3545';
        } else if (currentValue < 70) {
            element.style.color = '#ffc107';
        } else {
            element.style.color = '#28a745';
        }

        if (progress < 1) {
            requestAnimationFrame(updatePercentage);
        }
    }

    requestAnimationFrame(updatePercentage);
}

// تفعيل/تعطيل الصلاحيات عند تغيير نوع المستخدم
document.addEventListener('DOMContentLoaded', function() {
    const isAdminCheckbox = document.getElementById('is_admin');
    const permissionCheckboxes = document.querySelectorAll('.permission-check-input');

    function toggleAdminPermissions() {
        if (isAdminCheckbox && isAdminCheckbox.checked) {
            permissionCheckboxes.forEach((checkbox, index) => {
                setTimeout(() => {
                    checkbox.checked = true;
                    checkbox.disabled = true;
                    checkbox.closest('.permission-check').style.opacity = '0.7';
                }, index * 30);
            });
        } else {
            permissionCheckboxes.forEach((checkbox, index) => {
                setTimeout(() => {
                    checkbox.disabled = false;
                    checkbox.closest('.permission-check').style.opacity = '1';
                }, index * 30);
            });
        }

        // تحديث الإحصائيات بعد التغيير
        setTimeout(() => {
            updatePermissionStats();
        }, permissionCheckboxes.length * 30 + 100);
    }

    // إضافة event listeners لجميع checkboxes لتحديث الإحصائيات
    permissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updatePermissionStats();
        });
    });

    // تطبيق التغيير عند تحميل الصفحة
    if (isAdminCheckbox) {
        toggleAdminPermissions();
        // تطبيق التغيير عند تغيير نوع المستخدم
        isAdminCheckbox.addEventListener('change', toggleAdminPermissions);
    }

    // تحديث الإحصائيات عند تحميل الصفحة
    updatePermissionStats();
});
</script>
{% endblock %}
