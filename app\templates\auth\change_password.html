{% extends "base.html" %}

{% block title %}Changer le mot de passe{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-primary text-white text-center py-4">
                <h3 class="mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    Changer le mot de passe
                </h3>
                <p class="mb-0 mt-2">Mise à jour de votre mot de passe</p>
            </div>
            
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-key text-primary" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">
                        Pour votre sécurité, veuillez entrer votre mot de passe actuel puis choisir un nouveau mot de passe.
                    </p>
                </div>

                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        {{ form.current_password.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-unlock text-primary"></i>
                            </span>
                            {{ form.current_password(class="form-control form-control-lg", placeholder="Votre mot de passe actuel") }}
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password-eye"></i>
                            </button>
                        </div>
                        {% if form.current_password.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.current_password.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <div class="mb-4">
                        {{ form.new_password.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-lock text-success"></i>
                            </span>
                            {{ form.new_password(class="form-control form-control-lg", placeholder="Minimum 6 caractères") }}
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password-eye"></i>
                            </button>
                        </div>
                        {% if form.new_password.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.new_password.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        {{ form.confirm_password.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-check-circle text-success"></i>
                            </span>
                            {{ form.confirm_password(class="form-control form-control-lg", placeholder="Répétez le nouveau mot de passe") }}
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password-eye"></i>
                            </button>
                        </div>
                        {% if form.confirm_password.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.confirm_password.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Strength Indicator -->
                    <div class="mb-4">
                        <div class="password-strength">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" id="strength-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted mt-1" id="strength-text">Force du nouveau mot de passe</small>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mb-4">
                        {{ form.submit(class="btn btn-primary btn-lg", id="submitBtn") }}
                    </div>
                </form>

                <div class="text-center">
                    <hr class="my-4">
                    <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </a>
                    <a href="{{ url_for('auth.request_password_reset') }}" class="btn btn-outline-warning">
                        <i class="fas fa-question-circle me-1"></i>
                        Mot de passe oublié ?
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="card mt-4 border-info">
            <div class="card-body">
                <h6 class="card-title text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Conseils pour un mot de passe sécurisé
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled mb-0 small text-muted">
                            <li><i class="fas fa-check text-success me-2"></i>Au moins 8 caractères</li>
                            <li><i class="fas fa-check text-success me-2"></i>Majuscules et minuscules</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled mb-0 small text-muted">
                            <li><i class="fas fa-check text-success me-2"></i>Chiffres et symboles</li>
                            <li><i class="fas fa-check text-success me-2"></i>Unique et personnel</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 6) strength += 20;
    if (password.length >= 8) strength += 20;
    if (/[a-z]/.test(password)) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 10;
    if (/[^A-Za-z0-9]/.test(password)) strength += 10;
    
    if (strength < 30) {
        text = 'Très faible';
        color = 'bg-danger';
    } else if (strength < 50) {
        text = 'Faible';
        color = 'bg-warning';
    } else if (strength < 70) {
        text = 'Moyen';
        color = 'bg-info';
    } else if (strength < 90) {
        text = 'Fort';
        color = 'bg-success';
    } else {
        text = 'Très fort';
        color = 'bg-success';
    }
    
    const bar = document.getElementById('strength-bar');
    const textEl = document.getElementById('strength-text');
    
    if (bar && textEl) {
        bar.style.width = strength + '%';
        bar.className = 'progress-bar ' + color;
        textEl.textContent = text;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    if (newPasswordField) {
        newPasswordField.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mise à jour...';
        
        // Re-enable after 5 seconds in case of error
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 5000);
    });
});
</script>
{% endblock %}
