{% extends 'base.html' %}

{% block title %}Devis - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice me-2"></i>Devis
    </h1>
    <a href="{{ url_for('quotes.create_interactive') }}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i>Nouveau devis
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if quotes %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Numéro</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Expiration</th>
                        <th>Montant</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for quote in quotes %}
                    <tr>
                        <td>{{ quote.quote_number }}</td>
                        <td>{{ quote.client.name }}</td>
                        <td>{{ quote.date.strftime('%d/%m/%Y') }}</td>
                        <td>{{ quote.expiration_date.strftime('%d/%m/%Y') if quote.expiration_date else '-' }}</td>
                        <td>{{ quote.total }} MAD</td>
                        <td>
                            {% if quote.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                            {% elif quote.status == 'sent' %}
                            <span class="badge bg-info">Envoyé</span>
                            {% elif quote.status == 'accepted' %}
                            <span class="badge bg-success">Accepté</span>
                            {% elif quote.status == 'rejected' %}
                            <span class="badge bg-danger">Refusé</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('quotes.show', id=quote.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('quotes.edit_interactive', id=quote.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('quotes.generate_pdf', id=quote.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Générer PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <button class="btn btn-sm btn-danger btn-delete" data-id="{{ quote.id }}" data-number="{{ quote.quote_number }}" title="Supprimer le devis">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun devis n'a été créé.
            <a href="{{ url_for('quotes.create') }}">Créer un devis</a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal handled by base.html -->

<!-- Delete functionality handled by base.html -->
{% endblock %}
