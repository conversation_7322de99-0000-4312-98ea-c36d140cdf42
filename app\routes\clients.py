from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app import db
from app.models import Client
from app.forms.client_forms import ClientForm

bp = Blueprint('clients', __name__)

@bp.route('/')
def index():
    """List all clients"""
    clients = Client.query.all()
    return render_template('clients/index.html', clients=clients)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    """Create a new client"""
    form = ClientForm()

    if form.validate_on_submit():
        client = Client(
            name=form.name.data,
            address=form.address.data,
            city=form.city.data,
            postal_code=form.postal_code.data,
            email=form.email.data,
            phone=form.phone.data,
            ice=form.ice.data
        )

        db.session.add(client)
        db.session.commit()

        flash('Client créé avec succès!', 'success')
        return redirect(url_for('clients.index'))

    return render_template('clients/create.html', form=form)

@bp.route('/<int:id>')
def view(id):
    """View client details"""
    client = Client.query.get_or_404(id)

    # Get recent quotes, invoices, and delivery notes
    try:
        recent_quotes = client.quotes.order_by(client.quotes.date.desc()).limit(5).all() if hasattr(client, 'quotes') else []
    except:
        recent_quotes = []

    try:
        recent_invoices = client.invoices.order_by(client.invoices.date.desc()).limit(5).all() if hasattr(client, 'invoices') else []
    except:
        recent_invoices = []

    try:
        recent_delivery_notes = client.delivery_notes.order_by(client.delivery_notes.date.desc()).limit(5).all() if hasattr(client, 'delivery_notes') else []
    except:
        recent_delivery_notes = []

    return render_template('clients/view.html',
                         client=client,
                         recent_quotes=recent_quotes,
                         recent_invoices=recent_invoices,
                         recent_delivery_notes=recent_delivery_notes)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    """Edit a client"""
    client = Client.query.get_or_404(id)
    form = ClientForm(obj=client)

    if form.validate_on_submit():
        client.name = form.name.data
        client.address = form.address.data
        client.city = form.city.data
        client.postal_code = form.postal_code.data
        client.email = form.email.data
        client.phone = form.phone.data
        client.ice = form.ice.data

        db.session.commit()
        flash('Client modifié avec succès!', 'success')
        return redirect(url_for('clients.view', id=client.id))

    return render_template('clients/edit.html', form=form, client=client)

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    """Delete a client"""
    client = Client.query.get_or_404(id)

    # Check if client has associated documents or equipment
    try:
        has_quotes = hasattr(client, 'quotes') and client.quotes.count() > 0
        has_invoices = hasattr(client, 'invoices') and client.invoices.count() > 0
        has_delivery_notes = hasattr(client, 'delivery_notes') and client.delivery_notes.count() > 0
        has_equipment = hasattr(client, 'equipment_maintenance') and client.equipment_maintenance.count() > 0

        if has_quotes or has_invoices or has_delivery_notes or has_equipment:
            error_msg = 'Impossible de supprimer ce client car il a des éléments associés:'
            if has_quotes:
                error_msg += f' {client.quotes.count()} devis,'
            if has_invoices:
                error_msg += f' {client.invoices.count()} factures,'
            if has_delivery_notes:
                error_msg += f' {client.delivery_notes.count()} bons de livraison,'
            if has_equipment:
                error_msg += f' {client.equipment_maintenance.count()} équipements de maintenance,'

            # Remove trailing comma
            error_msg = error_msg.rstrip(',') + '.'

            flash(error_msg, 'error')
            return redirect(url_for('clients.index'))

    except Exception as e:
        print(f"Error checking client relationships: {e}")
        # Continue with deletion if relationship check fails
        pass

    try:
        db.session.delete(client)
        db.session.commit()
        flash('Client supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression du client: {str(e)}', 'error')

    return redirect(url_for('clients.index'))

@bp.route('/api/search')
def api_search():
    """API endpoint to search clients"""
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    clients = Client.query.filter(
        Client.name.contains(query) |
        Client.email.contains(query)
    ).limit(10).all()

    return jsonify([{
        'id': c.id,
        'name': c.name,
        'email': c.email,
        'phone': c.phone,
        'city': c.city
    } for c in clients])
