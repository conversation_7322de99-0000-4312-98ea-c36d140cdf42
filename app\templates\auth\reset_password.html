{% extends "base.html" %}

{% block title %}Réinitialiser le mot de passe{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-success text-white text-center py-4">
                <h3 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Nouveau mot de passe
                </h3>
                <p class="mb-0 mt-2">Créez votre nouveau mot de passe sécurisé</p>
            </div>
            
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-key text-success" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">
                        Choisissez un mot de passe fort et sécurisé pour votre compte.
                    </p>
                </div>

                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        {{ form.password.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-lock text-success"></i>
                            </span>
                            {{ form.password(class="form-control form-control-lg", placeholder="Minimum 6 caractères") }}
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.password.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        {{ form.password2.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-check-circle text-success"></i>
                            </span>
                            {{ form.password2(class="form-control form-control-lg", placeholder="Répétez le mot de passe") }}
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password2')">
                                <i class="fas fa-eye" id="password2-eye"></i>
                            </button>
                        </div>
                        {% if form.password2.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.password2.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Strength Indicator -->
                    <div class="mb-4">
                        <div class="password-strength">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" id="strength-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted mt-1" id="strength-text">Force du mot de passe</small>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mb-4">
                        {{ form.submit(class="btn btn-success btn-lg", id="submitBtn") }}
                    </div>
                </form>

                <div class="text-center">
                    <hr class="my-4">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Retour à la connexion
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="card mt-4 border-warning">
            <div class="card-body">
                <h6 class="card-title text-warning">
                    <i class="fas fa-shield-alt me-1"></i>
                    Conseils de sécurité
                </h6>
                <ul class="list-unstyled mb-0 small text-muted">
                    <li><i class="fas fa-check text-success me-2"></i>Utilisez au moins 8 caractères</li>
                    <li><i class="fas fa-check text-success me-2"></i>Mélangez majuscules et minuscules</li>
                    <li><i class="fas fa-check text-success me-2"></i>Incluez des chiffres et symboles</li>
                    <li><i class="fas fa-check text-success me-2"></i>Évitez les mots du dictionnaire</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 6) strength += 20;
    if (password.length >= 8) strength += 20;
    if (/[a-z]/.test(password)) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 10;
    if (/[^A-Za-z0-9]/.test(password)) strength += 10;
    
    if (strength < 30) {
        text = 'Très faible';
        color = 'bg-danger';
    } else if (strength < 50) {
        text = 'Faible';
        color = 'bg-warning';
    } else if (strength < 70) {
        text = 'Moyen';
        color = 'bg-info';
    } else if (strength < 90) {
        text = 'Fort';
        color = 'bg-success';
    } else {
        text = 'Très fort';
        color = 'bg-success';
    }
    
    const bar = document.getElementById('strength-bar');
    const textEl = document.getElementById('strength-text');
    
    bar.style.width = strength + '%';
    bar.className = 'progress-bar ' + color;
    textEl.textContent = text;
}

document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mise à jour...';
        
        // Re-enable after 5 seconds in case of error
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 5000);
    });
});
</script>
{% endblock %}
