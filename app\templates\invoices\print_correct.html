<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture N° {{ invoice.invoice_number if invoice else 'FACT-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        /* Header avec logo et titre */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 2px solid #dc3545;
        }

        .logo-section {
            flex: 1;
        }

        .logo-container {
            width: 80px;
            height: 80px;
            border: 2px solid #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            color: #dc3545;
            overflow: hidden;
        }

        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .document-title {
            font-size: 18pt;
            font-weight: bold;
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
        }

        .document-info {
            text-align: right;
            flex: 1;
        }

        .document-info div {
            margin-bottom: 5px;
        }

        /* Section client et informations */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }

        .client-info, .invoice-info {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .client-info h3, .invoice-info h3 {
            margin: 0 0 10px 0;
            color: #dc3545;
            font-size: 12pt;
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
        }

        .client-info p, .invoice-info p {
            margin: 5px 0;
            font-size: 10pt;
        }

        /* Tableau des articles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 10pt;
        }

        .items-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 10pt;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section totaux */
        .totals-section {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            width: 300px;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .totals-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: right;
        }

        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }

        .totals-table .total-row {
            background-color: #dc3545;
            color: white;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 9pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="goToInvoices()">Fermer</button>
    </div>

    <!-- Header avec logo et titre -->
    <div class="header">
        <div class="logo-section">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                {% else %}
                    Logo
                {% endif %}
            </div>
        </div>

        <div class="document-title">FACTURE</div>

        <div class="document-info">
            <div><strong>N° Facture:</strong> {{ invoice.invoice_number if invoice else 'FACT-001' }}</div>
            <div><strong>Date:</strong> {{ invoice.date.strftime('%d/%m/%Y') if invoice and invoice.date else '01/01/2024' }}</div>
            {% if invoice and invoice.due_date %}
            <div><strong>Échéance:</strong> {{ invoice.due_date.strftime('%d/%m/%Y') }}</div>
            {% endif %}
        </div>
    </div>

    <!-- Section client et informations -->
    <div class="info-section">
        <div class="client-info">
            <h3>FACTURER À</h3>
            <p><strong>{{ invoice.client.name if invoice and invoice.client else 'Nom du client' }}</strong></p>
            {% if invoice and invoice.client %}
                {% if invoice.client.address %}<p>{{ invoice.client.address }}</p>{% endif %}
                {% if invoice.client.city %}<p>{{ invoice.client.city }}</p>{% endif %}
                {% if invoice.client.phone %}<p>Tél: {{ invoice.client.phone }}</p>{% endif %}
                {% if invoice.client.email %}<p>Email: {{ invoice.client.email }}</p>{% endif %}
                {% if invoice.client.ice %}<p>ICE: {{ invoice.client.ice }}</p>{% endif %}
            {% endif %}
        </div>

        <div class="invoice-info">
            <h3>INFORMATIONS FACTURE</h3>
            <p><strong>Référence:</strong> {{ invoice.invoice_number if invoice else 'FACT-001' }}</p>
            <p><strong>Date émission:</strong> {{ invoice.date.strftime('%d/%m/%Y') if invoice and invoice.date else '01/01/2024' }}</p>
            {% if invoice and invoice.due_date %}
            <p><strong>Date échéance:</strong> {{ invoice.due_date.strftime('%d/%m/%Y') }}</p>
            {% endif %}
            {% if invoice and invoice.notes %}
            <p><strong>Notes:</strong> {{ invoice.notes }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Tableau des articles -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 15%;">Quantité</th>
                <th style="width: 15%;">Prix U.HT</th>
                <th style="width: 20%;">Prix T.HT</th>
            </tr>
        </thead>
        <tbody>
            {% if invoice and invoice.items %}
                {% for item in invoice.items %}
                <tr>
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ "%.2f"|format(item.unit_price) }} DH</td>
                    <td>{{ "%.2f"|format(item.quantity * item.unit_price) }} DH</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section totaux -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Total HT:</td>
                <td class="amount">{{ "%.2f"|format(invoice.total_amount / 1.2 if invoice and invoice.total_amount else 0) }} DH</td>
            </tr>
            <tr>
                <td class="label">TVA (20%):</td>
                <td class="amount">{{ "%.2f"|format((invoice.total_amount / 1.2) * 0.2 if invoice and invoice.total_amount else 0) }} DH</td>
            </tr>
            <tr class="total-row">
                <td class="label">Total TTC:</td>
                <td class="amount">{{ "%.2f"|format(invoice.total_amount if invoice and invoice.total_amount else 0) }} DH</td>
            </tr>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% elif company %}
            {% if company.name %}{{ company.name }}{% endif %}
            {% if company.address %} - {{ company.address }}{% endif %}
            {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
            {% if company.email %} - Email: {{ company.email }}{% endif %}
            {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
        {% endif %}
    </div>

    <script>
        function goToInvoices() {
            // Try multiple methods to navigate back
            try {
                // Method 1: Go to invoices page
                window.location.href = '/commercial/invoices';
            } catch (e) {
                try {
                    // Method 2: Go back in history
                    window.history.back();
                } catch (e2) {
                    try {
                        // Method 3: Close window if it's a popup
                        window.close();
                    } catch (e3) {
                        // Method 4: Fallback to dashboard
                        window.location.href = '/';
                    }
                }
            }
        }
    </script>
</body>
</html>
