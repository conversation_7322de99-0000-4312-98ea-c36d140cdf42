<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Buttons Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">🔧 Test des Boutons</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5>🗑️ Boutons de Suppression</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Test Client 1</td>
                                    <td>
                                        <button class="btn btn-danger btn-sm" 
                                                data-delete-url="/clients/1/delete"
                                                data-item-name="Test Client 1">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Test Facture 2</td>
                                    <td>
                                        <a href="/invoices/2/delete" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash-alt"></i> Supprimer
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5>🚪 Boutons de Fermeture</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-secondary" onclick="window.close()">
                                <i class="fas fa-times"></i> Fermer (onclick)
                            </button>
                            
                            <button class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Fermer (auto-detect)
                            </button>
                            
                            <a href="#" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-undo"></i> Retour (onclick)
                            </a>
                            
                            <button class="btn btn-secondary" onclick="fermer()">
                                <i class="fas fa-door-open"></i> Fermer (function)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5>📋 Console de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-console" class="bg-dark text-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace;">
                            <div>🚀 Debug Console Started...</div>
                        </div>
                        <button class="btn btn-primary mt-2" onclick="clearConsole()">Clear Console</button>
                        <button class="btn btn-success mt-2" onclick="testAllButtons()">Test All Buttons</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Debug Script -->
    <script>
        const debugConsole = document.getElementById('debug-console');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugConsole.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        function clearConsole() {
            debugConsole.innerHTML = '<div>🚀 Debug Console Cleared...</div>';
        }
        
        function testAllButtons() {
            log('🧪 Testing all buttons...');
            
            // Test delete buttons
            const deleteButtons = document.querySelectorAll('.btn-danger, .btn-outline-danger');
            log(`Found ${deleteButtons.length} delete buttons`);
            
            deleteButtons.forEach((btn, index) => {
                log(`Delete button ${index + 1}: ${btn.textContent.trim()}`);
                log(`  - Has onclick: ${btn.hasAttribute('onclick')}`);
                log(`  - Has data-delete-url: ${btn.hasAttribute('data-delete-url')}`);
                log(`  - Has href: ${btn.href || 'none'}`);
            });
            
            // Test close buttons
            const closeButtons = document.querySelectorAll('.btn-secondary');
            log(`Found ${closeButtons.length} close buttons`);
            
            closeButtons.forEach((btn, index) => {
                log(`Close button ${index + 1}: ${btn.textContent.trim()}`);
                log(`  - Has onclick: ${btn.hasAttribute('onclick')}`);
            });
        }
        
        // Override console.log to show in our debug console
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '));
        };
        
        // Test immediate fix
        log('🔧 Loading immediate fix...');
        
        // IMMEDIATE BUTTON FIX
        function immediateButtonFix() {
            log('⚡ Applying immediate button fix...');
            
            // Fix delete buttons
            document.querySelectorAll('.btn-danger, .btn-outline-danger').forEach(function(btn, index) {
                if (!btn.hasAttribute('data-immediate-fixed')) {
                    btn.setAttribute('data-immediate-fixed', 'true');
                    btn.removeAttribute('onclick');
                    
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        log(`🗑️ Delete button ${index + 1} clicked!`);
                        
                        const itemName = getItemNameQuick(this);
                        const deleteUrl = getDeleteUrlQuick(this);
                        
                        log(`Item: ${itemName}, URL: ${deleteUrl}`);
                        
                        if (confirm('⚠️ SUPPRIMER ' + itemName + ' ?\n\n❌ Action irréversible !')) {
                            log('✅ User confirmed deletion');
                            if (deleteUrl) {
                                log('📤 Would submit form to: ' + deleteUrl);
                                // Don't actually submit in test
                                alert('✅ Test réussi! La suppression fonctionnerait.');
                            } else {
                                log('❌ No delete URL found');
                                alert('❌ URL de suppression non trouvée');
                            }
                        } else {
                            log('❌ User cancelled deletion');
                        }
                    });
                    log(`✅ Fixed delete button ${index + 1}`);
                }
            });
            
            // Fix close buttons
            document.querySelectorAll('.btn-secondary').forEach(function(btn, index) {
                if (!btn.hasAttribute('data-immediate-fixed')) {
                    btn.setAttribute('data-immediate-fixed', 'true');
                    
                    // Don't remove onclick for test buttons
                    if (!btn.textContent.includes('onclick')) {
                        btn.removeAttribute('onclick');
                    }
                    
                    btn.addEventListener('click', function(e) {
                        if (!this.textContent.includes('onclick')) {
                            e.preventDefault();
                            e.stopPropagation();
                        }
                        
                        log(`🚪 Close button ${index + 1} clicked!`);
                        
                        if (!this.textContent.includes('onclick')) {
                            log('🔄 Would execute close function');
                            alert('✅ Test réussi! La fermeture fonctionnerait.');
                        }
                    });
                    log(`✅ Fixed close button ${index + 1}`);
                }
            });
        }
        
        function getItemNameQuick(btn) {
            const row = btn.closest('tr');
            if (row) {
                const cells = row.querySelectorAll('td');
                if (cells.length > 1) return cells[1].textContent.trim();
            }
            return btn.getAttribute('data-item-name') || 'cet élément';
        }
        
        function getDeleteUrlQuick(btn) {
            if (btn.hasAttribute('data-delete-url')) {
                return btn.getAttribute('data-delete-url');
            }
            if (btn.tagName === 'A') return btn.href;
            const form = btn.closest('form');
            if (form) return form.action;
            return null;
        }
        
        // Apply fix when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 DOM loaded, applying fixes...');
            immediateButtonFix();
            setTimeout(immediateButtonFix, 100);
            setTimeout(immediateButtonFix, 500);
        });
        
        // Apply fix immediately if DOM already loaded
        if (document.readyState !== 'loading') {
            log('📄 DOM already loaded, applying fixes...');
            immediateButtonFix();
        }
        
        log('✅ Debug system ready!');
    </script>
</body>
</html>
